import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import '../../core/errors/failures.dart';
import '../entities/user_preferences.dart';

abstract class PreferencesRepository {
  /// Get user preferences
  Future<Either<Failure, UserPreferences>> getUserPreferences();
  
  /// Save user preferences
  Future<Either<Failure, void>> saveUserPreferences(UserPreferences preferences);
  
  /// Update selected categories
  Future<Either<Failure, void>> updateSelectedCategories(List<String> categories);
  
  /// Toggle notification enabled/disabled
  Future<Either<Failure, void>> toggleNotifications(bool enabled);
  
  /// Update notification times
  Future<Either<Failure, void>> updateNotificationTimes(List<TimeOfDay> times);
  
  /// Update notification count
  Future<Either<Failure, void>> updateNotificationCount(int count);
  
  /// Mark onboarding as complete
  Future<Either<Failure, void>> completeOnboarding();
  
  /// Update preferred language
  Future<Either<Failure, void>> updatePreferredLanguage(String language);
  
  /// Toggle dark mode
  Future<Either<Failure, void>> toggleDarkMode(bool enabled);
  
  /// Update last quote date
  Future<Either<Failure, void>> updateLastQuoteDate(DateTime date);
  
  /// Add quote to favorites
  Future<Either<Failure, void>> addFavoriteQuote(String quoteId);
  
  /// Remove quote from favorites
  Future<Either<Failure, void>> removeFavoriteQuote(String quoteId);
  
  /// Clear all preferences (reset to defaults)
  Future<Either<Failure, void>> clearPreferences();
  
  /// Check if onboarding is complete
  Future<Either<Failure, bool>> isOnboardingComplete();
}
