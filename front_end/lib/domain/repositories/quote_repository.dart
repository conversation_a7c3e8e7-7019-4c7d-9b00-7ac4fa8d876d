import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/quote.dart';

abstract class QuoteRepository {
  /// Get a random quote from the specified categories
  Future<Either<Failure, Quote>> getRandomQuote(List<String> categories);
  
  /// Get quotes by category
  Future<Either<Failure, List<Quote>>> getQuotesByCategory(String category);
  
  /// Get all available categories
  Future<Either<Failure, List<String>>> getAvailableCategories();
  
  /// Search quotes by text or author
  Future<Either<Failure, List<Quote>>> searchQuotes(String query);
  
  /// Get favorite quotes
  Future<Either<Failure, List<Quote>>> getFavoriteQuotes();
  
  /// Add quote to favorites
  Future<Either<Failure, void>> addToFavorites(String quoteId);
  
  /// Remove quote from favorites
  Future<Either<Failure, void>> removeFromFavorites(String quoteId);
  
  /// Get quote by ID
  Future<Either<Failure, Quote>> getQuoteById(String id);
  
  /// Cache quotes locally
  Future<Either<Failure, void>> cacheQuotes(List<Quote> quotes);
  
  /// Get cached quotes
  Future<Either<Failure, List<Quote>>> getCachedQuotes();
  
  /// Clear quote cache
  Future<Either<Failure, void>> clearCache();
}
