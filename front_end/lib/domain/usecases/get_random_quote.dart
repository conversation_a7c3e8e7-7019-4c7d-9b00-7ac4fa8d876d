import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/quote.dart';
import '../repositories/quote_repository.dart';

class GetRandomQuote implements UseCase<Quote, GetRandomQuoteParams> {
  final QuoteRepository repository;

  GetRandomQuote(this.repository);

  @override
  Future<Either<Failure, Quote>> call(GetRandomQuoteParams params) async {
    return await repository.getRandomQuote(params.categories);
  }
}

class GetRandomQuoteParams {
  final List<String> categories;

  GetRandomQuoteParams({required this.categories});
}
