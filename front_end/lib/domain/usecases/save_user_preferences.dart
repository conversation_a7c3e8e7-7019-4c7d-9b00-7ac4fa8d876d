import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/user_preferences.dart';
import '../repositories/preferences_repository.dart';

class SaveUserPreferences implements UseCase<void, SaveUserPreferencesParams> {
  final PreferencesRepository repository;

  SaveUserPreferences(this.repository);

  @override
  Future<Either<Failure, void>> call(SaveUserPreferencesParams params) async {
    return await repository.saveUserPreferences(params.preferences);
  }
}

class SaveUserPreferencesParams {
  final UserPreferences preferences;

  SaveUserPreferencesParams({required this.preferences});
}
