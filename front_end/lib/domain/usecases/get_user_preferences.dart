import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/usecases/usecase.dart';
import '../entities/user_preferences.dart';
import '../repositories/preferences_repository.dart';

class GetUserPreferences implements UseCase<UserPreferences, NoParams> {
  final PreferencesRepository repository;

  GetUserPreferences(this.repository);

  @override
  Future<Either<Failure, UserPreferences>> call(NoParams params) async {
    return await repository.getUserPreferences();
  }
}
