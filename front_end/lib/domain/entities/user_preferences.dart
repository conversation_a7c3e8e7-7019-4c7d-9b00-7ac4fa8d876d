import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class UserPreferences extends Equatable {
  final List<String> selectedCategories;
  final bool notificationEnabled;
  final List<TimeOfDay> notificationTimes;
  final int notificationCount;
  final bool onboardingComplete;
  final String preferredLanguage;
  final bool darkModeEnabled;
  final DateTime lastQuoteDate;
  final List<String> favoriteQuoteIds;

  const UserPreferences({
    this.selectedCategories = const [],
    this.notificationEnabled = true,
    this.notificationTimes = const [TimeOfDay(hour: 9, minute: 0)],
    this.notificationCount = 3,
    this.onboardingComplete = false,
    this.preferredLanguage = 'en',
    this.darkModeEnabled = false,
    required this.lastQuoteDate,
    this.favoriteQuoteIds = const [],
  });

  UserPreferences copyWith({
    List<String>? selectedCategories,
    bool? notificationEnabled,
    List<TimeOfDay>? notificationTimes,
    int? notificationCount,
    bool? onboardingComplete,
    String? preferredLanguage,
    bool? darkModeEnabled,
    DateTime? lastQuoteDate,
    List<String>? favoriteQuoteIds,
  }) {
    return UserPreferences(
      selectedCategories: selectedCategories ?? this.selectedCategories,
      notificationEnabled: notificationEnabled ?? this.notificationEnabled,
      notificationTimes: notificationTimes ?? this.notificationTimes,
      notificationCount: notificationCount ?? this.notificationCount,
      onboardingComplete: onboardingComplete ?? this.onboardingComplete,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
      lastQuoteDate: lastQuoteDate ?? this.lastQuoteDate,
      favoriteQuoteIds: favoriteQuoteIds ?? this.favoriteQuoteIds,
    );
  }

  @override
  List<Object?> get props => [
        selectedCategories,
        notificationEnabled,
        notificationTimes,
        notificationCount,
        onboardingComplete,
        preferredLanguage,
        darkModeEnabled,
        lastQuoteDate,
        favoriteQuoteIds,
      ];

  @override
  String toString() {
    return 'UserPreferences(selectedCategories: $selectedCategories, notificationEnabled: $notificationEnabled, notificationTimes: $notificationTimes, notificationCount: $notificationCount, onboardingComplete: $onboardingComplete, preferredLanguage: $preferredLanguage, darkModeEnabled: $darkModeEnabled, lastQuoteDate: $lastQuoteDate, favoriteQuoteIds: $favoriteQuoteIds)';
  }
}
