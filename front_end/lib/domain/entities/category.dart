import 'package:equatable/equatable.dart';

class Category extends Equatable {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String color;
  final String icon;
  final String? imageUrl;
  final bool isActive;
  final int sortOrder;
  final int quoteCount;
  final int popularityScore;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Category({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.color,
    required this.icon,
    this.imageUrl,
    this.isActive = true,
    this.sortOrder = 0,
    this.quoteCount = 0,
    this.popularityScore = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  Category copyWith({
    int? id,
    String? name,
    String? slug,
    String? description,
    String? color,
    String? icon,
    String? imageUrl,
    bool? isActive,
    int? sortOrder,
    int? quoteCount,
    int? popularityScore,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      slug: slug ?? this.slug,
      description: description ?? this.description,
      color: color ?? this.color,
      icon: icon ?? this.icon,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      quoteCount: quoteCount ?? this.quoteCount,
      popularityScore: popularityScore ?? this.popularityScore,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        slug,
        description,
        color,
        icon,
        imageUrl,
        isActive,
        sortOrder,
        quoteCount,
        popularityScore,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'Category(id: $id, name: $name, slug: $slug, description: $description, color: $color, icon: $icon, imageUrl: $imageUrl, isActive: $isActive, sortOrder: $sortOrder, quoteCount: $quoteCount, popularityScore: $popularityScore, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
