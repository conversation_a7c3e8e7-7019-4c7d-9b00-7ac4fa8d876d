import 'package:flutter/foundation.dart';

/// Platform detection utilities that work across all Flutter targets
class PlatformUtils {
  
  /// Check if running on Android
  static bool get isAndroid {
    if (kIsWeb) return false;
    return defaultTargetPlatform == TargetPlatform.android;
  }
  
  /// Check if running on iOS
  static bool get isIOS {
    if (kIsWeb) return false;
    return defaultTargetPlatform == TargetPlatform.iOS;
  }
  
  /// Check if running on Linux
  static bool get isLinux {
    if (kIsWeb) return false;
    return defaultTargetPlatform == TargetPlatform.linux;
  }
  
  /// Check if running on Windows
  static bool get isWindows {
    if (kIsWeb) return false;
    return defaultTargetPlatform == TargetPlatform.windows;
  }
  
  /// Check if running on macOS
  static bool get isMacOS {
    if (kIsWeb) return false;
    return defaultTargetPlatform == TargetPlatform.macOS;
  }
  
  /// Check if running on Fuchsia
  static bool get isFuchsia {
    if (kIsWeb) return false;
    return defaultTargetPlatform == TargetPlatform.fuchsia;
  }
  
  /// Check if running on web
  static bool get isWeb => kIsWeb;
  
  /// Check if running on mobile (Android or iOS)
  static bool get isMobile => isAndroid || isIOS;
  
  /// Check if running on desktop (Linux, Windows, or macOS)
  static bool get isDesktop => isLinux || isWindows || isMacOS;
  
  /// Get platform name as string
  static String get platformName {
    if (kIsWeb) return 'Web';
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return 'Android';
      case TargetPlatform.iOS:
        return 'iOS';
      case TargetPlatform.linux:
        return 'Linux';
      case TargetPlatform.windows:
        return 'Windows';
      case TargetPlatform.macOS:
        return 'macOS';
      case TargetPlatform.fuchsia:
        return 'Fuchsia';
    }
  }
  
  /// Check if notifications are supported on this platform
  static bool get supportsNotifications {
    // Web doesn't support local notifications in the same way
    if (kIsWeb) return false;
    
    // All other platforms support notifications
    return isMobile || isDesktop;
  }
  
  /// Check if platform requires permission requests for notifications
  static bool get requiresNotificationPermissions {
    // Only mobile platforms require explicit permission requests
    return isMobile;
  }
  
  /// Check if platform supports background notifications
  static bool get supportsBackgroundNotifications {
    // Web and some desktop environments may not support background notifications
    if (kIsWeb) return false;
    return isMobile || isDesktop;
  }
  
  /// Get appropriate notification channel importance for platform
  static int getNotificationImportance() {
    if (isAndroid) {
      return 4; // IMPORTANCE_HIGH
    }
    return 3; // Default importance
  }
  
  /// Check if platform supports notification scheduling
  static bool get supportsNotificationScheduling {
    if (kIsWeb) return false;
    return isMobile || isDesktop;
  }
  
  /// Get environment variables (safe for all platforms)
  static Map<String, String> getEnvironmentVariables() {
    if (kIsWeb) return <String, String>{};
    
    try {
      // This will only work on platforms that support it
      if (isDesktop) {
        // For desktop platforms, we can try to access environment variables
        // but we need to handle it safely
        return <String, String>{
          'DESKTOP_SESSION': _getEnvVar('DESKTOP_SESSION') ?? 'unknown',
          'XDG_CURRENT_DESKTOP': _getEnvVar('XDG_CURRENT_DESKTOP') ?? 'unknown',
        };
      }
    } catch (e) {
      // Silently handle any errors
    }
    
    return <String, String>{};
  }
  
  /// Safely get environment variable
  static String? _getEnvVar(String key) {
    try {
      // This is a placeholder - in a real implementation, you'd use
      // dart:io Platform.environment, but only on supported platforms
      return null;
    } catch (e) {
      return null;
    }
  }
  
  /// Check if running in debug mode
  static bool get isDebugMode => kDebugMode;
  
  /// Check if running in release mode
  static bool get isReleaseMode => kReleaseMode;
  
  /// Check if running in profile mode
  static bool get isProfileMode => kProfileMode;
}
