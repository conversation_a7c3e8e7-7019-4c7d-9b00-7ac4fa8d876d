import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Utility class to generate placeholder images and gradients
/// This replaces actual image assets for demo purposes
class ImageGenerator {
  static const List<List<Color>> _categoryGradients = [
    [Color(0xFF6B73FF), Color(0xFF9B59B6)], // motivation - purple/blue
    [Color(0xFFFF6B6B), Color(0xFFFFE66D)], // success - red/yellow
    [Color(0xFF4ECDC4), Color(0xFF44A08D)], // life - teal/green
    [Color(0xFFFFB347), Color(0xFFFFCC02)], // wisdom - orange/yellow
    [Color(0xFFFF9A9E), Color(0xFFFECFEF)], // happiness - pink
    [Color(0xFFA8EDEA), Color(0xFFFED6E3)], // inspiration - cyan/pink
    [Color(0xFF667eea), Color(0xFF764ba2)], // leadership - blue/purple
    [Color(0xFF96fbc4), Color(0xFFF9f047)], // growth - green/yellow
    [Color(0xFFa8caba), Color(0xFF5d4e75)], // mindfulness - sage/purple
    [Color(0xFFfc4a1a), Color(0xFFf7b733)], // courage - red/orange
  ];

  static const List<List<Color>> _backgroundGradients = [
    [Color(0xFF667eea), Color(0xFF764ba2)],
    [Color(0xFFf093fb), Color(0xFFf5576c)],
    [Color(0xFF4facfe), Color(0xFF00f2fe)],
    [Color(0xFF43e97b), Color(0xFF38f9d7)],
    [Color(0xFFfa709a), Color(0xFFfee140)],
    [Color(0xFFa8edea), Color(0xFFfed6e3)],
    [Color(0xFFffecd2), Color(0xFFfcb69f)],
  ];

  static const List<List<Color>> _achievementGradients = [
    [Color(0xFFFFD700), Color(0xFFFFA500)], // gold
    [Color(0xFFFF4500), Color(0xFFFF6347)], // fire
    [Color(0xFF9370DB), Color(0xFF8A2BE2)], // purple
    [Color(0xFF32CD32), Color(0xFF228B22)], // green
    [Color(0xFF1E90FF), Color(0xFF4169E1)], // blue
    [Color(0xFFFF1493), Color(0xFFFF69B4)], // pink
    [Color(0xFFFF8C00), Color(0xFFFFD700)], // orange
  ];

  /// Generate a category image widget
  static Widget generateCategoryImage({
    required String category,
    required double width,
    required double height,
    BorderRadius? borderRadius,
  }) {
    final gradientIndex = category.hashCode.abs() % _categoryGradients.length;
    final gradient = _categoryGradients[gradientIndex];
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
        ),
        boxShadow: [
          BoxShadow(
            color: gradient.first.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Pattern overlay
          Positioned.fill(
            child: CustomPaint(
              painter: PatternPainter(
                color: Colors.white.withOpacity(0.1),
                pattern: _getCategoryPattern(category),
              ),
            ),
          ),
          // Category icon
          Center(
            child: Icon(
              _getCategoryIcon(category),
              size: math.min(width, height) * 0.4,
              color: Colors.white,
            ),
          ),
          // Category name
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Text(
              category.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
                letterSpacing: 1,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// Generate a background image widget
  static Widget generateBackgroundImage({
    required int index,
    required double width,
    required double height,
    BorderRadius? borderRadius,
  }) {
    final gradientIndex = index % _backgroundGradients.length;
    final gradient = _backgroundGradients[gradientIndex];
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradient,
          stops: const [0.0, 1.0],
        ),
      ),
      child: CustomPaint(
        painter: BackgroundPatternPainter(
          color: Colors.white.withOpacity(0.1),
          pattern: index % 3,
        ),
      ),
    );
  }

  /// Generate an achievement badge
  static Widget generateAchievementBadge({
    required String achievementKey,
    required double size,
    bool isUnlocked = true,
  }) {
    final gradientIndex = achievementKey.hashCode.abs() % _achievementGradients.length;
    final gradient = _achievementGradients[gradientIndex];
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: isUnlocked 
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: gradient,
              )
            : LinearGradient(
                colors: [Colors.grey.shade400, Colors.grey.shade600],
              ),
        boxShadow: [
          BoxShadow(
            color: isUnlocked 
                ? gradient.first.withOpacity(0.4)
                : Colors.grey.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Achievement icon
          Center(
            child: Icon(
              _getAchievementIcon(achievementKey),
              size: size * 0.5,
              color: Colors.white,
            ),
          ),
          // Lock overlay for locked achievements
          if (!isUnlocked)
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withOpacity(0.6),
              ),
              child: Center(
                child: Icon(
                  Icons.lock,
                  size: size * 0.3,
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Generate an avatar
  static Widget generateAvatar({
    required int userLevel,
    required double size,
  }) {
    final colors = _getAvatarColors(userLevel);
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: colors,
        ),
        border: Border.all(
          color: colors.first,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: colors.first.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Avatar pattern
          Positioned.fill(
            child: ClipOval(
              child: CustomPaint(
                painter: AvatarPatternPainter(
                  color: Colors.white.withOpacity(0.2),
                  level: userLevel,
                ),
              ),
            ),
          ),
          // Level indicator
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: colors.first,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: Text(
                '$userLevel',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get category icon
  static IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'motivation':
        return Icons.rocket_launch;
      case 'success':
        return Icons.emoji_events;
      case 'life':
        return Icons.favorite;
      case 'wisdom':
        return Icons.psychology;
      case 'happiness':
        return Icons.sentiment_very_satisfied;
      case 'inspiration':
        return Icons.lightbulb;
      case 'leadership':
        return Icons.groups;
      case 'growth':
        return Icons.trending_up;
      case 'mindfulness':
        return Icons.self_improvement;
      case 'courage':
        return Icons.shield;
      default:
        return Icons.format_quote;
    }
  }

  /// Get category pattern
  static int _getCategoryPattern(String category) {
    return category.hashCode.abs() % 4;
  }

  /// Get achievement icon
  static IconData _getAchievementIcon(String achievementKey) {
    switch (achievementKey) {
      case 'first_quote':
        return Icons.star;
      case 'streak_3':
      case 'streak_7':
      case 'streak_30':
        return Icons.local_fire_department;
      case 'category_explorer':
        return Icons.explore;
      case 'quote_master':
        return Icons.school;
      case 'early_bird':
        return Icons.wb_sunny;
      case 'night_owl':
        return Icons.nightlight;
      case 'weekend_warrior':
        return Icons.weekend;
      case 'perfect_week':
        return Icons.check_circle;
      default:
        return Icons.emoji_events;
    }
  }

  /// Get avatar colors based on level
  static List<Color> _getAvatarColors(int level) {
    if (level >= 50) return [const Color(0xFF9C27B0), const Color(0xFF673AB7)];
    if (level >= 30) return [const Color(0xFFFF9800), const Color(0xFFFF5722)];
    if (level >= 20) return [const Color(0xFFF44336), const Color(0xFFE91E63)];
    if (level >= 10) return [const Color(0xFF2196F3), const Color(0xFF3F51B5)];
    if (level >= 5) return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)];
    return [const Color(0xFF9E9E9E), const Color(0xFF607D8B)];
  }
}

/// Custom painter for patterns
class PatternPainter extends CustomPainter {
  final Color color;
  final int pattern;

  PatternPainter({required this.color, required this.pattern});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    switch (pattern) {
      case 0: // Dots
        _paintDots(canvas, size, paint);
        break;
      case 1: // Lines
        _paintLines(canvas, size, paint);
        break;
      case 2: // Circles
        _paintCircles(canvas, size, paint);
        break;
      case 3: // Grid
        _paintGrid(canvas, size, paint);
        break;
    }
  }

  void _paintDots(Canvas canvas, Size size, Paint paint) {
    for (double x = 10; x < size.width; x += 20) {
      for (double y = 10; y < size.height; y += 20) {
        canvas.drawCircle(Offset(x, y), 2, paint);
      }
    }
  }

  void _paintLines(Canvas canvas, Size size, Paint paint) {
    for (double x = 0; x < size.width; x += 15) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
  }

  void _paintCircles(Canvas canvas, Size size, Paint paint) {
    paint.style = PaintingStyle.stroke;
    for (double x = 20; x < size.width; x += 40) {
      for (double y = 20; y < size.height; y += 40) {
        canvas.drawCircle(Offset(x, y), 8, paint);
      }
    }
  }

  void _paintGrid(Canvas canvas, Size size, Paint paint) {
    for (double x = 0; x < size.width; x += 10) {
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
    }
    for (double y = 0; y < size.height; y += 10) {
      canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter for background patterns
class BackgroundPatternPainter extends CustomPainter {
  final Color color;
  final int pattern;

  BackgroundPatternPainter({required this.color, required this.pattern});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    switch (pattern) {
      case 0:
        _paintWaves(canvas, size, paint);
        break;
      case 1:
        _paintDiagonals(canvas, size, paint);
        break;
      case 2:
        _paintHexagons(canvas, size, paint);
        break;
    }
  }

  void _paintWaves(Canvas canvas, Size size, Paint paint) {
    final path = Path();
    for (double y = 0; y < size.height; y += 30) {
      path.moveTo(0, y);
      for (double x = 0; x < size.width; x += 20) {
        path.quadraticBezierTo(x + 10, y - 10, x + 20, y);
      }
    }
    paint.style = PaintingStyle.stroke;
    canvas.drawPath(path, paint);
  }

  void _paintDiagonals(Canvas canvas, Size size, Paint paint) {
    for (double i = -size.height; i < size.width; i += 20) {
      canvas.drawLine(Offset(i, 0), Offset(i + size.height, size.height), paint);
    }
  }

  void _paintHexagons(Canvas canvas, Size size, Paint paint) {
    paint.style = PaintingStyle.stroke;
    const hexSize = 15.0;
    for (double x = hexSize; x < size.width; x += hexSize * 1.5) {
      for (double y = hexSize; y < size.height; y += hexSize * math.sqrt(3)) {
        _drawHexagon(canvas, Offset(x, y), hexSize, paint);
      }
    }
  }

  void _drawHexagon(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path();
    for (int i = 0; i < 6; i++) {
      final angle = (i * math.pi) / 3;
      final x = center.dx + size * math.cos(angle);
      final y = center.dy + size * math.sin(angle);
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter for avatar patterns
class AvatarPatternPainter extends CustomPainter {
  final Color color;
  final int level;

  AvatarPatternPainter({required this.color, required this.level});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1;

    // Different patterns based on level
    if (level >= 30) {
      _paintStars(canvas, size, paint);
    } else if (level >= 20) {
      _paintDiamonds(canvas, size, paint);
    } else if (level >= 10) {
      _paintTriangles(canvas, size, paint);
    } else {
      _paintSimpleDots(canvas, size, paint);
    }
  }

  void _paintStars(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 4;
    
    for (int i = 0; i < 5; i++) {
      final angle = (i * 2 * math.pi) / 5 - math.pi / 2;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      _drawStar(canvas, Offset(x, y), 4, paint);
    }
  }

  void _paintDiamonds(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width / 2, size.height / 2);
    for (int i = 0; i < 8; i++) {
      final angle = (i * 2 * math.pi) / 8;
      final radius = size.width / 3;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      _drawDiamond(canvas, Offset(x, y), 3, paint);
    }
  }

  void _paintTriangles(Canvas canvas, Size size, Paint paint) {
    for (double x = 10; x < size.width; x += 15) {
      for (double y = 10; y < size.height; y += 15) {
        _drawTriangle(canvas, Offset(x, y), 3, paint);
      }
    }
  }

  void _paintSimpleDots(Canvas canvas, Size size, Paint paint) {
    for (double x = 8; x < size.width; x += 16) {
      for (double y = 8; y < size.height; y += 16) {
        canvas.drawCircle(Offset(x, y), 1, paint);
      }
    }
  }

  void _drawStar(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path();
    for (int i = 0; i < 10; i++) {
      final angle = (i * math.pi) / 5;
      final radius = i.isEven ? size : size / 2;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  void _drawDiamond(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path()
      ..moveTo(center.dx, center.dy - size)
      ..lineTo(center.dx + size, center.dy)
      ..lineTo(center.dx, center.dy + size)
      ..lineTo(center.dx - size, center.dy)
      ..close();
    canvas.drawPath(path, paint);
  }

  void _drawTriangle(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path()
      ..moveTo(center.dx, center.dy - size)
      ..lineTo(center.dx + size, center.dy + size)
      ..lineTo(center.dx - size, center.dy + size)
      ..close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
