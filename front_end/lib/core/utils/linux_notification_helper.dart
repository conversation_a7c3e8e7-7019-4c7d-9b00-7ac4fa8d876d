import 'package:flutter/material.dart';
import 'platform_utils.dart';

/// Helper class for Linux notification troubleshooting and setup
class LinuxNotificationHelper {
  
  /// Check if notification daemon is available on Linux
  static Future<bool> isNotificationDaemonAvailable() async {
    if (!PlatformUtils.isLinux) return true;

    // On web or non-Linux platforms, assume notifications work
    if (PlatformUtils.isWeb || !PlatformUtils.isLinux) {
      return true;
    }

    // For Linux desktop, we can't easily check daemon availability in Flutter web
    // Return true and let the actual notification attempt determine success
    return true;
  }
  
  /// Get notification daemon status and recommendations
  static Future<NotificationStatus> getNotificationStatus() async {
    if (!PlatformUtils.isLinux) {
      return NotificationStatus(
        isAvailable: true,
        daemon: 'System',
        message: 'Notifications supported on this platform',
      );
    }

    if (PlatformUtils.isWeb) {
      return NotificationStatus(
        isAvailable: false,
        daemon: 'Web Platform',
        message: 'Local notifications are not supported in web browsers',
        recommendations: ['Use the web app for reading quotes', 'Install the desktop app for notifications'],
      );
    }

    // For Linux desktop, provide general guidance since we can't check processes on web
    return NotificationStatus(
      isAvailable: true,
      daemon: 'Unknown',
      message: 'Linux desktop detected. Notifications should work if a daemon is installed.',
      recommendations: _getGenericRecommendations(),
    );
  }
  
  /// Get installation recommendations based on desktop environment
  // ignore: unused_element
  static List<String> _getRecommendations(String desktop) {
    final lowerDesktop = desktop.toLowerCase();
    
    if (lowerDesktop.contains('gnome')) {
      return [
        'GNOME should have built-in notifications',
        'Try: sudo apt install notification-daemon',
        'Or restart your GNOME session',
      ];
    } else if (lowerDesktop.contains('kde') || lowerDesktop.contains('plasma')) {
      return [
        'KDE should have built-in notifications',
        'Check System Settings > Notifications',
        'Try: sudo apt install plasma-workspace',
      ];
    } else if (lowerDesktop.contains('xfce')) {
      return [
        'Install XFCE notification daemon:',
        'sudo apt install xfce4-notifyd',
        'Or try: sudo apt install notification-daemon',
      ];
    } else if (lowerDesktop.contains('i3') || lowerDesktop.contains('sway')) {
      return [
        'For i3/Sway, install a notification daemon:',
        'sudo apt install dunst  # For X11',
        'sudo apt install mako-notifier  # For Wayland',
        'Add to your config to start automatically',
      ];
    } else {
      return _getGenericRecommendations();
    }
  }
  
  /// Get generic installation recommendations
  static List<String> _getGenericRecommendations() {
    return [
      'Install a notification daemon:',
      'sudo apt install dunst  # Lightweight, works everywhere',
      'sudo apt install notification-daemon  # Standard daemon',
      'sudo apt install notify-osd  # Ubuntu-style notifications',
      '',
      'For Wayland users:',
      'sudo apt install mako-notifier',
      '',
      'After installation, restart your session or run:',
      'dunst &  # or your chosen daemon',
    ];
  }
  
  /// Test notification functionality
  static Future<bool> testNotification() async {
    if (!PlatformUtils.isLinux) return true;

    if (PlatformUtils.isWeb) {
      // Can't test notifications on web
      return false;
    }

    // For Linux desktop, we can't run shell commands from Flutter web
    // Return true and let the actual notification system handle the test
    return true;
  }
  
  /// Show notification setup dialog
  static void showNotificationSetupDialog(BuildContext context, NotificationStatus status) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              status.isAvailable ? Icons.check_circle : Icons.warning,
              color: status.isAvailable ? Colors.green : Colors.orange,
            ),
            const SizedBox(width: 8),
            const Text('Notification Status'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Daemon: ${status.daemon}'),
              const SizedBox(height: 8),
              Text(status.message),
              if (status.recommendations.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Recommendations:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...status.recommendations.map((rec) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    rec,
                    style: TextStyle(
                      fontFamily: rec.startsWith('sudo') ? 'monospace' : null,
                      fontSize: rec.startsWith('sudo') ? 12 : null,
                    ),
                  ),
                )),
              ],
            ],
          ),
        ),
        actions: [
          if (PlatformUtils.isLinux)
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                final success = await testNotification();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        success 
                          ? 'Test notification sent successfully!' 
                          : 'Test notification failed. Check your notification daemon.',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Test Notification'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Notification status information
class NotificationStatus {
  final bool isAvailable;
  final String daemon;
  final String message;
  final List<String> recommendations;
  
  NotificationStatus({
    required this.isAvailable,
    required this.daemon,
    required this.message,
    this.recommendations = const [],
  });
}
