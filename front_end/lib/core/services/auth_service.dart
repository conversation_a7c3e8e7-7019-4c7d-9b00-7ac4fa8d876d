import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _refreshTokenKey = 'refresh_token';

  String? _token;
  Map<String, dynamic>? _userData;

  // Get current auth token
  String? get token => _token;

  // Get current user data
  Map<String, dynamic>? get userData => _userData;

  // Check if user is authenticated
  bool get isAuthenticated => _token != null && _token!.isNotEmpty;

  // Initialize auth service (load saved token)
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _token = prefs.getString(_tokenKey);
    
    final userDataString = prefs.getString(_userKey);
    if (userDataString != null) {
      try {
        _userData = jsonDecode(userDataString) as Map<String, dynamic>;
      } catch (e) {
        // Clear invalid user data
        await clearUserData();
      }
    }
  }

  // Save authentication token
  Future<void> saveToken(String token) async {
    _token = token;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  // Save user data
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    _userData = userData;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(userData));
  }

  // Save refresh token
  Future<void> saveRefreshToken(String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  // Get refresh token
  Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  // Clear all authentication data
  Future<void> clearAuth() async {
    _token = null;
    _userData = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userKey);
    await prefs.remove(_refreshTokenKey);
  }

  // Clear user data only
  Future<void> clearUserData() async {
    _userData = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
  }

  // Get authorization header
  Map<String, String> getAuthHeaders() {
    if (_token != null) {
      return {
        'Authorization': 'Bearer $_token',
      };
    }
    return {};
  }

  // Check if token is expired (basic check)
  bool isTokenExpired() {
    if (_token == null) return true;
    
    try {
      // Decode JWT token to check expiration
      final parts = _token!.split('.');
      if (parts.length != 3) return true;
      
      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final payloadMap = jsonDecode(decoded) as Map<String, dynamic>;
      
      final exp = payloadMap['exp'] as int?;
      if (exp == null) return true;
      
      final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      return DateTime.now().isAfter(expirationDate);
    } catch (e) {
      return true;
    }
  }

  // Get user ID from token
  String? getUserId() {
    if (_userData != null) {
      return _userData!['id']?.toString();
    }
    
    try {
      final parts = _token?.split('.');
      if (parts == null || parts.length != 3) return null;
      
      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final payloadMap = jsonDecode(decoded) as Map<String, dynamic>;
      
      return payloadMap['sub']?.toString() ?? payloadMap['user_id']?.toString();
    } catch (e) {
      return null;
    }
  }

  // Get user email
  String? getUserEmail() {
    return _userData?['email']?.toString();
  }

  // Get user name
  String? getUserName() {
    return _userData?['full_name']?.toString() ?? _userData?['username']?.toString();
  }

  // Check if user is admin
  bool isAdmin() {
    return _userData?['is_superuser'] == true || _userData?['role'] == 'admin';
  }

  // Update user data field
  Future<void> updateUserData(String key, dynamic value) async {
    if (_userData != null) {
      _userData![key] = value;
      await saveUserData(_userData!);
    }
  }
}
