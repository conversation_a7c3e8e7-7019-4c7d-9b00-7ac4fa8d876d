import 'dart:convert';
import 'package:share_plus/share_plus.dart';
import '../utils/platform_utils.dart';
import 'storage_service.dart';
import 'logging_service.dart';

/// Production-level backup and restore service
class BackupService {
  static const String _backupVersion = '1.0.0';
  
  /// Create a complete backup of all app data
  static Future<BackupResult> createBackup({
    bool includeUserData = true,
    bool includeAnalytics = true,
    bool includePreferences = true,
    bool includeGamification = true,
  }) async {
    try {
      LoggingService.info('Creating backup...');
      
      final backup = <String, dynamic>{
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'platform': PlatformUtils.platformName,
        'appVersion': '1.0.0', // This should come from package info
      };
      
      if (includeUserData) {
        backup['userData'] = StorageService.loadUserData();
      }
      
      if (includeAnalytics) {
        backup['analytics'] = StorageService.loadAnalyticsData();
      }
      
      if (includePreferences) {
        backup['preferences'] = StorageService.loadPreferencesData();
      }
      
      if (includeGamification) {
        backup['gamification'] = StorageService.loadGamificationData();
      }
      
      // Add metadata
      backup['metadata'] = {
        'totalSize': jsonEncode(backup).length,
        'dataTypes': backup.keys.where((k) => k != 'version' && k != 'timestamp' && k != 'platform' && k != 'appVersion' && k != 'metadata').toList(),
        'createdOn': PlatformUtils.platformName,
      };
      
      LoggingService.info('Backup created successfully', data: {
        'size': backup['metadata']['totalSize'],
        'types': backup['metadata']['dataTypes'],
      });
      
      return BackupResult.success(backup);
    } catch (e) {
      LoggingService.error('Failed to create backup', error: e);
      return BackupResult.error('Failed to create backup: ${e.toString()}');
    }
  }
  
  /// Restore data from backup
  static Future<RestoreResult> restoreFromBackup(
    Map<String, dynamic> backup, {
    bool restoreUserData = true,
    bool restoreAnalytics = true,
    bool restorePreferences = true,
    bool restoreGamification = true,
  }) async {
    try {
      LoggingService.info('Restoring from backup...');
      
      // Validate backup format
      final validation = _validateBackup(backup);
      if (!validation.isValid) {
        return RestoreResult.error(validation.error!);
      }
      
      final restoredItems = <String>[];
      
      if (restoreUserData && backup.containsKey('userData')) {
        await StorageService.saveUserData(backup['userData'] as Map<String, dynamic>);
        restoredItems.add('User Data');
      }
      
      if (restoreAnalytics && backup.containsKey('analytics')) {
        await StorageService.saveAnalyticsData(backup['analytics'] as Map<String, dynamic>);
        restoredItems.add('Analytics');
      }
      
      if (restorePreferences && backup.containsKey('preferences')) {
        await StorageService.savePreferencesData(backup['preferences'] as Map<String, dynamic>);
        restoredItems.add('Preferences');
      }
      
      if (restoreGamification && backup.containsKey('gamification')) {
        await StorageService.saveGamificationData(backup['gamification'] as Map<String, dynamic>);
        restoredItems.add('Gamification');
      }
      
      LoggingService.info('Backup restored successfully', data: {
        'restoredItems': restoredItems,
      });
      
      return RestoreResult.success(restoredItems);
    } catch (e) {
      LoggingService.error('Failed to restore backup', error: e);
      return RestoreResult.error('Failed to restore backup: ${e.toString()}');
    }
  }
  
  /// Export backup as JSON string
  static Future<String> exportBackupAsJson(Map<String, dynamic> backup) async {
    try {
      return jsonEncode(backup);
    } catch (e) {
      LoggingService.error('Failed to export backup as JSON', error: e);
      throw Exception('Failed to export backup: ${e.toString()}');
    }
  }
  
  /// Import backup from JSON string
  static Future<Map<String, dynamic>> importBackupFromJson(String jsonString) async {
    try {
      final backup = jsonDecode(jsonString) as Map<String, dynamic>;
      
      final validation = _validateBackup(backup);
      if (!validation.isValid) {
        throw Exception(validation.error);
      }
      
      return backup;
    } catch (e) {
      LoggingService.error('Failed to import backup from JSON', error: e);
      throw Exception('Failed to import backup: ${e.toString()}');
    }
  }
  
  /// Share backup file
  static Future<void> shareBackup(Map<String, dynamic> backup) async {
    try {
      final jsonString = await exportBackupAsJson(backup);
      final timestamp = DateTime.now().toIso8601String().split('T')[0];
      final fileName = 'daily_motivator_backup_$timestamp.json';
      
      await Share.share(
        jsonString,
        subject: 'Daily Motivator Backup - $timestamp',
      );
      
      LoggingService.logUserAction('share_backup', data: {
        'fileName': fileName,
        'size': jsonString.length,
      });
    } catch (e) {
      LoggingService.error('Failed to share backup', error: e);
      throw Exception('Failed to share backup: ${e.toString()}');
    }
  }
  
  /// Get backup statistics
  static Map<String, dynamic> getBackupStats(Map<String, dynamic> backup) {
    try {
      final jsonString = jsonEncode(backup);
      final dataTypes = backup.keys.where((k) => 
        k != 'version' && 
        k != 'timestamp' && 
        k != 'platform' && 
        k != 'appVersion' && 
        k != 'metadata'
      ).toList();
      
      final typeSizes = <String, int>{};
      for (final type in dataTypes) {
        if (backup[type] != null) {
          typeSizes[type] = jsonEncode(backup[type]).length;
        }
      }
      
      return {
        'totalSize': jsonString.length,
        'totalSizeFormatted': _formatBytes(jsonString.length),
        'dataTypes': dataTypes,
        'typeSizes': typeSizes,
        'createdAt': backup['timestamp'],
        'platform': backup['platform'],
        'version': backup['version'],
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }
  
  /// Validate backup format and integrity
  static BackupValidation _validateBackup(Map<String, dynamic> backup) {
    try {
      // Check required fields
      if (!backup.containsKey('version')) {
        return BackupValidation.invalid('Missing version field');
      }
      
      if (!backup.containsKey('timestamp')) {
        return BackupValidation.invalid('Missing timestamp field');
      }
      
      // Check version compatibility
      final version = backup['version'] as String;
      if (!_isVersionCompatible(version)) {
        return BackupValidation.invalid('Incompatible backup version: $version');
      }
      
      // Check timestamp validity
      try {
        DateTime.parse(backup['timestamp'] as String);
      } catch (e) {
        return BackupValidation.invalid('Invalid timestamp format');
      }
      
      // Check data integrity
      final dataTypes = ['userData', 'analytics', 'preferences', 'gamification'];
      bool hasData = false;
      
      for (final type in dataTypes) {
        if (backup.containsKey(type)) {
          hasData = true;
          if (backup[type] is! Map<String, dynamic>) {
            return BackupValidation.invalid('Invalid data format for $type');
          }
        }
      }
      
      if (!hasData) {
        return BackupValidation.invalid('Backup contains no data');
      }
      
      return BackupValidation.valid();
    } catch (e) {
      return BackupValidation.invalid('Backup validation failed: ${e.toString()}');
    }
  }
  
  /// Check if backup version is compatible
  static bool _isVersionCompatible(String version) {
    // For now, only support current version
    // In the future, implement version migration logic
    return version == _backupVersion;
  }
  
  /// Format bytes to human readable string
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  /// Create automatic backup (for scheduled backups)
  static Future<BackupResult> createAutoBackup() async {
    LoggingService.info('Creating automatic backup...');
    
    final result = await createBackup();
    
    if (result.isSuccess) {
      // Save auto backup to storage
      await StorageService.saveData('auto_backup', result.backup);
      await StorageService.saveData('auto_backup_timestamp', DateTime.now().toIso8601String());
      
      LoggingService.info('Automatic backup saved');
    }
    
    return result;
  }
  
  /// Get last auto backup info
  static Map<String, dynamic>? getLastAutoBackupInfo() {
    try {
      final timestamp = StorageService.loadData<String>('auto_backup_timestamp');
      if (timestamp == null) return null;
      
      final backup = StorageService.loadData<Map<String, dynamic>>('auto_backup');
      if (backup == null) return null;
      
      return {
        'timestamp': timestamp,
        'stats': getBackupStats(backup),
      };
    } catch (e) {
      LoggingService.error('Failed to get auto backup info', error: e);
      return null;
    }
  }
}

/// Backup operation result
class BackupResult {
  final bool isSuccess;
  final Map<String, dynamic>? backup;
  final String? error;
  
  BackupResult.success(this.backup) : isSuccess = true, error = null;
  BackupResult.error(this.error) : isSuccess = false, backup = null;
}

/// Restore operation result
class RestoreResult {
  final bool isSuccess;
  final List<String>? restoredItems;
  final String? error;
  
  RestoreResult.success(this.restoredItems) : isSuccess = true, error = null;
  RestoreResult.error(this.error) : isSuccess = false, restoredItems = null;
}

/// Backup validation result
class BackupValidation {
  final bool isValid;
  final String? error;
  
  BackupValidation.valid() : isValid = true, error = null;
  BackupValidation.invalid(this.error) : isValid = false;
}
