import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorageService {
  static const String _favoritesKey = 'guest_favorites';
  static const String _preferencesKey = 'guest_preferences';
  static const String _statsKey = 'guest_stats';
  static const String _quotesHistoryKey = 'guest_quotes_history';
  static const String _lastQuoteDateKey = 'guest_last_quote_date';

  static LocalStorageService? _instance;
  static SharedPreferences? _prefs;

  LocalStorageService._();

  static Future<LocalStorageService> getInstance() async {
    _instance ??= LocalStorageService._();
    _prefs ??= await SharedPreferences.getInstance();
    return _instance!;
  }

  // Favorite Quotes Management
  Future<List<int>> getFavoriteQuoteIds() async {
    final favoritesJson = _prefs!.getString(_favoritesKey);
    if (favoritesJson == null) return [];
    
    try {
      final List<dynamic> favoritesList = jsonDecode(favoritesJson);
      return favoritesList.cast<int>();
    } catch (e) {
      return [];
    }
  }

  Future<void> addToFavorites(int quoteId) async {
    final favorites = await getFavoriteQuoteIds();
    if (!favorites.contains(quoteId)) {
      favorites.add(quoteId);
      await _prefs!.setString(_favoritesKey, jsonEncode(favorites));
    }
  }

  Future<void> removeFromFavorites(int quoteId) async {
    final favorites = await getFavoriteQuoteIds();
    favorites.remove(quoteId);
    await _prefs!.setString(_favoritesKey, jsonEncode(favorites));
  }

  Future<bool> isFavorite(int quoteId) async {
    final favorites = await getFavoriteQuoteIds();
    return favorites.contains(quoteId);
  }

  // User Preferences
  Future<Map<String, dynamic>> getGuestPreferences() async {
    final prefsJson = _prefs!.getString(_preferencesKey);
    if (prefsJson == null) {
      return _getDefaultPreferences();
    }
    
    try {
      return jsonDecode(prefsJson) as Map<String, dynamic>;
    } catch (e) {
      return _getDefaultPreferences();
    }
  }

  Future<void> saveGuestPreferences(Map<String, dynamic> preferences) async {
    await _prefs!.setString(_preferencesKey, jsonEncode(preferences));
  }

  Map<String, dynamic> _getDefaultPreferences() {
    return {
      'selectedCategories': <int>[],
      'notificationEnabled': true,
      'notificationTimes': ['09:00', '18:00'],
      'darkModeEnabled': false,
      'language': 'en',
      'onboardingComplete': false,
    };
  }

  // Statistics
  Future<Map<String, dynamic>> getGuestStats() async {
    final statsJson = _prefs!.getString(_statsKey);
    if (statsJson == null) {
      return _getDefaultStats();
    }
    
    try {
      return jsonDecode(statsJson) as Map<String, dynamic>;
    } catch (e) {
      return _getDefaultStats();
    }
  }

  Future<void> updateGuestStats(Map<String, dynamic> stats) async {
    await _prefs!.setString(_statsKey, jsonEncode(stats));
  }

  Map<String, dynamic> _getDefaultStats() {
    return {
      'quotesRead': 0,
      'favoriteCount': 0,
      'currentStreak': 0,
      'longestStreak': 0,
      'totalDays': 0,
      'categoriesExplored': <int>[],
    };
  }

  // Quote History
  Future<List<Map<String, dynamic>>> getQuoteHistory() async {
    final historyJson = _prefs!.getString(_quotesHistoryKey);
    if (historyJson == null) return [];
    
    try {
      final List<dynamic> historyList = jsonDecode(historyJson);
      return historyList.cast<Map<String, dynamic>>();
    } catch (e) {
      return [];
    }
  }

  Future<void> addToQuoteHistory(Map<String, dynamic> quote) async {
    final history = await getQuoteHistory();
    
    // Add timestamp
    quote['viewedAt'] = DateTime.now().toIso8601String();
    
    // Add to beginning of list
    history.insert(0, quote);
    
    // Keep only last 100 quotes
    if (history.length > 100) {
      history.removeRange(100, history.length);
    }
    
    await _prefs!.setString(_quotesHistoryKey, jsonEncode(history));
    
    // Update stats
    await _updateQuoteStats();
  }

  Future<void> _updateQuoteStats() async {
    final stats = await getGuestStats();
    final history = await getQuoteHistory();
    final favorites = await getFavoriteQuoteIds();
    
    stats['quotesRead'] = history.length;
    stats['favoriteCount'] = favorites.length;
    
    // Calculate streak
    final today = DateTime.now();
    final lastQuoteDate = await getLastQuoteDate();
    
    if (lastQuoteDate != null) {
      final daysDifference = today.difference(lastQuoteDate).inDays;
      
      if (daysDifference == 0) {
        // Same day, maintain streak
      } else if (daysDifference == 1) {
        // Next day, increment streak
        stats['currentStreak'] = (stats['currentStreak'] ?? 0) + 1;
      } else {
        // Streak broken
        stats['currentStreak'] = 1;
      }
    } else {
      // First quote
      stats['currentStreak'] = 1;
    }
    
    // Update longest streak
    if (stats['currentStreak'] > (stats['longestStreak'] ?? 0)) {
      stats['longestStreak'] = stats['currentStreak'];
    }
    
    // Update categories explored
    final categoriesExplored = Set<int>.from(stats['categoriesExplored'] ?? []);
    for (final quote in history) {
      if (quote['categoryId'] != null) {
        categoriesExplored.add(quote['categoryId'] as int);
      }
    }
    stats['categoriesExplored'] = categoriesExplored.toList();
    
    await updateGuestStats(stats);
    await setLastQuoteDate(today);
  }

  // Last Quote Date
  Future<DateTime?> getLastQuoteDate() async {
    final dateString = _prefs!.getString(_lastQuoteDateKey);
    if (dateString == null) return null;
    
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  Future<void> setLastQuoteDate(DateTime date) async {
    await _prefs!.setString(_lastQuoteDateKey, date.toIso8601String());
  }

  // Clear all guest data
  Future<void> clearGuestData() async {
    await _prefs!.remove(_favoritesKey);
    await _prefs!.remove(_preferencesKey);
    await _prefs!.remove(_statsKey);
    await _prefs!.remove(_quotesHistoryKey);
    await _prefs!.remove(_lastQuoteDateKey);
  }

  // Export guest data (for syncing when user logs in)
  Future<Map<String, dynamic>> exportGuestData() async {
    return {
      'favorites': await getFavoriteQuoteIds(),
      'preferences': await getGuestPreferences(),
      'stats': await getGuestStats(),
      'history': await getQuoteHistory(),
      'lastQuoteDate': (await getLastQuoteDate())?.toIso8601String(),
    };
  }

  // Import data (when syncing from cloud)
  Future<void> importData(Map<String, dynamic> data) async {
    if (data['favorites'] != null) {
      await _prefs!.setString(_favoritesKey, jsonEncode(data['favorites']));
    }
    
    if (data['preferences'] != null) {
      await _prefs!.setString(_preferencesKey, jsonEncode(data['preferences']));
    }
    
    if (data['stats'] != null) {
      await _prefs!.setString(_statsKey, jsonEncode(data['stats']));
    }
    
    if (data['history'] != null) {
      await _prefs!.setString(_quotesHistoryKey, jsonEncode(data['history']));
    }
    
    if (data['lastQuoteDate'] != null) {
      await _prefs!.setString(_lastQuoteDateKey, data['lastQuoteDate']);
    }
  }

  // Check if user has any local data
  Future<bool> hasLocalData() async {
    final favorites = await getFavoriteQuoteIds();
    final history = await getQuoteHistory();
    final prefs = await getGuestPreferences();
    
    return favorites.isNotEmpty || 
           history.isNotEmpty || 
           prefs['onboardingComplete'] == true;
  }
}
