import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../models/asset_models.dart';
import '../../domain/entities/quote.dart';
import 'logging_service.dart';

/// Service for sharing content including images, quotes, and combined content
class SharingService {
  static const String _shareDirectory = 'shared_content';

  /// Share a quote as text
  static Future<void> shareQuote(Quote quote) async {
    try {
      final text = '"${quote.text}"\n\n- ${quote.author}';
      await Share.share(
        text,
        subject: 'Daily Motivation',
      );
      
      LoggingService.logUserAction('share_quote', data: {
        'quote_id': quote.id,
        'author': quote.author,
      });
    } catch (e) {
      LoggingService.error('Failed to share quote: $e');
      rethrow;
    }
  }

  /// Share an image asset
  static Future<void> shareImage(LocalAsset asset) async {
    try {
      final file = File(asset.localPath);
      if (!await file.exists()) {
        throw Exception('Image file not found: ${asset.localPath}');
      }

      await Share.shareXFiles(
        [XFile(asset.localPath)],
        text: asset.metadata?.title ?? 'Shared from Daily Motivator',
        subject: 'Daily Motivator Image',
      );
      
      LoggingService.logUserAction('share_image', data: {
        'asset_id': asset.id,
        'category': asset.category.name,
      });
    } catch (e) {
      LoggingService.error('Failed to share image: $e');
      rethrow;
    }
  }

  /// Create and share a quote with background image
  static Future<void> shareQuoteWithImage(
    Quote quote,
    LocalAsset backgroundAsset, {
    Color? textColor,
    Color? backgroundColor,
    String? fontFamily,
    double? fontSize,
  }) async {
    try {
      final imageFile = await _createQuoteImage(
        quote,
        backgroundAsset,
        textColor: textColor ?? Colors.white,
        backgroundColor: backgroundColor,
        fontFamily: fontFamily,
        fontSize: fontSize ?? 24.0,
      );

      await Share.shareXFiles(
        [XFile(imageFile.path)],
        text: '"${quote.text}" - ${quote.author}',
        subject: 'Daily Motivation',
      );
      
      LoggingService.logUserAction('share_quote_with_image', data: {
        'quote_id': quote.id,
        'background_asset_id': backgroundAsset.id,
      });
    } catch (e) {
      LoggingService.error('Failed to share quote with image: $e');
      rethrow;
    }
  }

  /// Create an image with quote text overlaid on background
  static Future<File> _createQuoteImage(
    Quote quote,
    LocalAsset backgroundAsset, {
    required Color textColor,
    Color? backgroundColor,
    String? fontFamily,
    required double fontSize,
  }) async {
    // Create a widget to render
    final widget = _QuoteImageWidget(
      quote: quote,
      backgroundAsset: backgroundAsset,
      textColor: textColor,
      backgroundColor: backgroundColor,
      fontFamily: fontFamily,
      fontSize: fontSize,
    );

    // Render widget to image
    final imageBytes = await _renderWidgetToImage(widget);
    
    // Save to temporary file
    final tempDir = await getTemporaryDirectory();
    final shareDir = Directory('${tempDir.path}/$_shareDirectory');
    if (!await shareDir.exists()) {
      await shareDir.create(recursive: true);
    }
    
    final fileName = 'quote_${DateTime.now().millisecondsSinceEpoch}.png';
    final file = File('${shareDir.path}/$fileName');
    await file.writeAsBytes(imageBytes);
    
    return file;
  }

  /// Render a widget to image bytes using a simpler approach
  static Future<Uint8List> _renderWidgetToImage(Widget widget) async {
    // This is a simplified approach - in a real implementation,
    // you would need to properly render the widget tree
    // For now, we'll create a placeholder image
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Draw a simple background
    final paint = Paint()..color = Colors.white;
    canvas.drawRect(const Rect.fromLTWH(0, 0, 800, 600), paint);

    // Draw placeholder text
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'Quote Image Placeholder',
        style: TextStyle(color: Colors.black, fontSize: 24),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(canvas, const Offset(100, 300));

    final picture = recorder.endRecording();
    final image = await picture.toImage(800, 600);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  /// Share multiple assets as a collection
  static Future<void> shareAssetCollection(List<LocalAsset> assets) async {
    try {
      final files = <XFile>[];
      
      for (final asset in assets) {
        final file = File(asset.localPath);
        if (await file.exists()) {
          files.add(XFile(asset.localPath));
        }
      }
      
      if (files.isEmpty) {
        throw Exception('No valid files to share');
      }

      await Share.shareXFiles(
        files,
        text: 'Shared ${files.length} images from Daily Motivator',
        subject: 'Daily Motivator Collection',
      );
      
      LoggingService.logUserAction('share_asset_collection', data: {
        'asset_count': files.length,
      });
    } catch (e) {
      LoggingService.error('Failed to share asset collection: $e');
      rethrow;
    }
  }

  /// Share app content (invite friends)
  static Future<void> shareApp() async {
    try {
      const text = '''
🌟 Start your day with inspiration! 🌟

I've been using Daily Motivator and it's amazing! Get personalized motivational quotes, beautiful backgrounds, and track your progress.

Download it now and transform your daily routine!

#DailyMotivation #Inspiration #PersonalGrowth
      ''';

      await Share.share(
        text,
        subject: 'Daily Motivator - Start Your Inspiration Journey',
      );
      
      LoggingService.logUserAction('share_app');
    } catch (e) {
      LoggingService.error('Failed to share app: $e');
      rethrow;
    }
  }

  /// Copy quote text to clipboard
  static Future<void> copyQuoteToClipboard(Quote quote) async {
    try {
      final text = '"${quote.text}"\n\n- ${quote.author}';
      await Clipboard.setData(ClipboardData(text: text));
      
      LoggingService.logUserAction('copy_quote', data: {
        'quote_id': quote.id,
      });
    } catch (e) {
      LoggingService.error('Failed to copy quote to clipboard: $e');
      rethrow;
    }
  }

  /// Save quote image to device gallery
  static Future<void> saveQuoteImageToGallery(
    Quote quote,
    LocalAsset backgroundAsset, {
    Color? textColor,
    Color? backgroundColor,
    String? fontFamily,
    double? fontSize,
  }) async {
    try {
      final imageFile = await _createQuoteImage(
        quote,
        backgroundAsset,
        textColor: textColor ?? Colors.white,
        backgroundColor: backgroundColor,
        fontFamily: fontFamily,
        fontSize: fontSize ?? 24.0,
      );

      // In a real app, you would use a plugin like image_gallery_saver
      // For now, we'll just log the action
      LoggingService.logUserAction('save_quote_image', data: {
        'quote_id': quote.id,
        'background_asset_id': backgroundAsset.id,
        'saved_path': imageFile.path,
      });
      
      LoggingService.info('Quote image saved: ${imageFile.path}');
    } catch (e) {
      LoggingService.error('Failed to save quote image: $e');
      rethrow;
    }
  }

  /// Clean up temporary share files
  static Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final shareDir = Directory('${tempDir.path}/$_shareDirectory');
      
      if (await shareDir.exists()) {
        final files = await shareDir.list().toList();
        final now = DateTime.now();
        
        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);
            
            // Delete files older than 24 hours
            if (age.inHours > 24) {
              await file.delete();
            }
          }
        }
      }
    } catch (e) {
      LoggingService.warning('Failed to cleanup temp files: $e');
    }
  }
}

/// Widget for rendering quote with background image
class _QuoteImageWidget extends StatelessWidget {
  final Quote quote;
  final LocalAsset backgroundAsset;
  final Color textColor;
  final Color? backgroundColor;
  final String? fontFamily;
  final double fontSize;

  const _QuoteImageWidget({
    required this.quote,
    required this.backgroundAsset,
    required this.textColor,
    this.backgroundColor,
    this.fontFamily,
    required this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 800,
      height: 600,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: FileImage(File(backgroundAsset.localPath)),
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor?.withOpacity(0.3),
          gradient: backgroundColor == null
              ? LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.3),
                    Colors.black.withOpacity(0.6),
                  ],
                )
              : null,
        ),
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '"${quote.text}"',
              style: TextStyle(
                color: textColor,
                fontSize: fontSize,
                fontWeight: FontWeight.w600,
                fontFamily: fontFamily,
                height: 1.4,
                shadows: [
                  Shadow(
                    offset: const Offset(2, 2),
                    blurRadius: 4,
                    color: Colors.black.withOpacity(0.5),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            Text(
              '- ${quote.author}',
              style: TextStyle(
                color: textColor.withOpacity(0.9),
                fontSize: fontSize * 0.7,
                fontWeight: FontWeight.w500,
                fontFamily: fontFamily,
                shadows: [
                  Shadow(
                    offset: const Offset(1, 1),
                    blurRadius: 2,
                    color: Colors.black.withOpacity(0.5),
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
            const Spacer(),
            Text(
              'Daily Motivator',
              style: TextStyle(
                color: textColor.withOpacity(0.7),
                fontSize: fontSize * 0.5,
                fontWeight: FontWeight.w400,
                fontFamily: fontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
