import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/network/api_client.dart';
import '../../core/services/notification_service.dart';
import '../../core/services/asset_management_service.dart';
import '../../core/services/auth_service.dart';
import '../../data/datasources/preferences_local_datasource.dart';
import '../../data/datasources/quote_remote_datasource.dart';
import '../../data/datasources/backend_quote_datasource.dart';
import '../../data/datasources/auth_datasource.dart';
import '../../data/repositories/preferences_repository_impl.dart';
import '../../data/repositories/backend_quote_repository_impl.dart';
import '../../domain/repositories/preferences_repository.dart';
import '../../domain/repositories/quote_repository.dart';
import '../../domain/usecases/get_random_quote.dart';
import '../../domain/usecases/get_user_preferences.dart';
import '../../domain/usecases/save_user_preferences.dart';
import '../../presentation/providers/notification_provider.dart';
import '../../presentation/providers/preferences_provider.dart';
import '../../presentation/providers/quote_provider.dart';
import '../../presentation/providers/theme_provider.dart';
import '../../presentation/providers/analytics_provider.dart';
import '../../presentation/providers/gamification_provider.dart';
import '../../presentation/providers/user_provider.dart';
import '../../presentation/providers/asset_provider.dart';
import '../../presentation/providers/auth_provider.dart';

class InjectionContainer {
  static final InjectionContainer _instance = InjectionContainer._internal();
  factory InjectionContainer() => _instance;
  InjectionContainer._internal();

  // Core
  late final http.Client httpClient;
  late final ApiClient apiClient;
  late final AuthService authService;
  late final NotificationService notificationService;
  late final AssetManagementService assetManagementService;
  late final SharedPreferences sharedPreferences;

  // Data sources
  late final QuoteRemoteDataSource quoteRemoteDataSource;
  late final BackendQuoteDataSource backendQuoteDataSource;
  late final AuthDataSource authDataSource;
  late final PreferencesLocalDataSource preferencesLocalDataSource;

  // Repositories
  late final QuoteRepository quoteRepository;
  late final PreferencesRepository preferencesRepository;

  // Use cases
  late final GetRandomQuote getRandomQuote;
  late final GetUserPreferences getUserPreferences;
  late final SaveUserPreferences saveUserPreferences;

  // Providers
  late final AuthProvider authProvider;
  late final QuoteProvider quoteProvider;
  late final PreferencesProvider preferencesProvider;
  late final NotificationProvider notificationProvider;
  late final ThemeProvider themeProvider;
  late final AnalyticsProvider analyticsProvider;
  late final GamificationProvider gamificationProvider;
  late final UserProvider userProvider;
  late final AssetProvider assetProvider;

  Future<void> init() async {
    // Core
    httpClient = http.Client();
    sharedPreferences = await SharedPreferences.getInstance();

    // Initialize auth service first
    authService = AuthService();
    await authService.initialize();

    // Initialize API client with auth service
    apiClient = ApiClient(
      client: httpClient,
      authService: authService,
    );

    notificationService = NotificationService();
    assetManagementService = AssetManagementService();

    // Initialize services
    await notificationService.initialize();
    await assetManagementService.initialize();

    // Data sources
    quoteRemoteDataSource = QuoteRemoteDataSourceImpl(apiClient: apiClient);
    backendQuoteDataSource = BackendQuoteDataSourceImpl(apiClient: apiClient);
    authDataSource = AuthDataSourceImpl(
      apiClient: apiClient,
      authService: authService,
    );
    preferencesLocalDataSource = PreferencesLocalDataSourceImpl(
      sharedPreferences: sharedPreferences,
    );

    // Repositories
    quoteRepository = BackendQuoteRepositoryImpl(backendDataSource: backendQuoteDataSource);
    preferencesRepository = PreferencesRepositoryImpl(
      localDataSource: preferencesLocalDataSource,
    );

    // Use cases
    getRandomQuote = GetRandomQuote(quoteRepository);
    getUserPreferences = GetUserPreferences(preferencesRepository);
    saveUserPreferences = SaveUserPreferences(preferencesRepository);

    // Providers
    authProvider = AuthProvider(
      authDataSource: authDataSource,
      authService: authService,
    );

    quoteProvider = QuoteProvider(getRandomQuote: getRandomQuote);
    preferencesProvider = PreferencesProvider(
      getUserPreferences: getUserPreferences,
      saveUserPreferences: saveUserPreferences,
    );
    notificationProvider = NotificationProvider(
      notificationService: notificationService,
      getRandomQuote: getRandomQuote,
    );
    themeProvider = ThemeProvider();

    // ProxyProvider-style providers that depend on other providers
    analyticsProvider = AnalyticsProvider(
      quoteProvider: quoteProvider,
      preferencesProvider: preferencesProvider,
    );
    gamificationProvider = GamificationProvider(
      analyticsProvider: analyticsProvider,
      quoteProvider: quoteProvider,
      preferencesProvider: preferencesProvider,
    );

    // Initialize user provider
    userProvider = UserProvider();
    await userProvider.initialize();

    // Initialize asset provider
    assetProvider = AssetProvider(assetManagementService);

    // Load initial preferences and theme
    await preferencesProvider.loadPreferences();
    await themeProvider.loadTheme();
  }

  void dispose() {
    httpClient.close();
  }
}
