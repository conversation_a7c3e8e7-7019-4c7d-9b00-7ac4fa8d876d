import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../errors/exceptions.dart';
import '../services/auth_service.dart';

class ApiClient {
  final http.Client client;
  final String baseUrl;
  final Duration timeout;
  final AuthService? authService;

  ApiClient({
    required this.client,
    this.baseUrl = 'http://localhost:8000/api/v1',
    this.timeout = const Duration(seconds: 30),
    this.authService,
  });

  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, String>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      final response = await client
          .get(
            uri,
            headers: _buildHeaders(headers),
          )
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException(
        message: 'No internet connection',
        code: 0,
      );
    } on http.ClientException catch (e) {
      throw NetworkException(
        message: 'Network error: ${e.message}',
        code: 0,
      );
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error: ${e.toString()}',
        code: 500,
      );
    }
  }

  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, String>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      final response = await client
          .post(
            uri,
            headers: _buildHeaders(headers),
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException(
        message: 'No internet connection',
        code: 0,
      );
    } on http.ClientException catch (e) {
      throw NetworkException(
        message: 'Network error: ${e.message}',
        code: 0,
      );
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error: ${e.toString()}',
        code: 500,
      );
    }
  }

  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    Map<String, String>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      final response = await client
          .put(
            uri,
            headers: _buildHeaders(headers),
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException(
        message: 'No internet connection',
        code: 0,
      );
    } on http.ClientException catch (e) {
      throw NetworkException(
        message: 'Network error: ${e.message}',
        code: 0,
      );
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error: ${e.toString()}',
        code: 500,
      );
    }
  }

  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, String>? queryParameters,
  }) async {
    try {
      final uri = _buildUri(endpoint, queryParameters);
      final response = await client
          .delete(
            uri,
            headers: _buildHeaders(headers),
          )
          .timeout(timeout);

      return _handleResponse(response);
    } on SocketException {
      throw NetworkException(
        message: 'No internet connection',
        code: 0,
      );
    } on http.ClientException catch (e) {
      throw NetworkException(
        message: 'Network error: ${e.message}',
        code: 0,
      );
    } catch (e) {
      throw ServerException(
        message: 'Unexpected error: ${e.toString()}',
        code: 500,
      );
    }
  }

  Uri _buildUri(String endpoint, Map<String, String>? queryParameters) {
    final uri = Uri.parse('$baseUrl$endpoint');
    if (queryParameters != null && queryParameters.isNotEmpty) {
      return uri.replace(queryParameters: queryParameters);
    }
    return uri;
  }

  Map<String, String> _buildHeaders(Map<String, String>? additionalHeaders) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add authentication headers if available
    if (authService != null && authService!.isAuthenticated) {
      headers.addAll(authService!.getAuthHeaders());
    }

    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    return headers;
  }

  Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        throw ServerException(
          message: 'Invalid JSON response',
          code: response.statusCode,
        );
      }
    } else {
      String errorMessage = 'Server error';
      try {
        final errorBody = jsonDecode(response.body) as Map<String, dynamic>;
        errorMessage = errorBody['message'] ?? errorMessage;
      } catch (e) {
        // Use default error message if JSON parsing fails
      }

      throw ServerException(
        message: errorMessage,
        code: response.statusCode,
      );
    }
  }
}
