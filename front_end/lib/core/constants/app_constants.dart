class AppConstants {
  // Available categories for motivational quotes
  static const List<String> availableCategories = [
    'success',
    'motivation',
    'inspiration',
    'happiness',
    'courage',
    'wisdom',
    'love',
    'leadership',
    'growth',
    'mindfulness',
  ];

  // App configuration constants
  static const String appName = 'Motivational App';
  static const String appVersion = '1.0.0';

  // Notification settings
  static const int defaultNotificationHour = 9;
  static const int defaultNotificationMinute = 0;
  static const int defaultNotificationCount = 3;
  static const int minNotifications = 1;
  static const int maxNotifications = 10;

  // Quote display settings
  static const int maxQuotesPerDay = 5;
  static const Duration quoteCacheDuration = Duration(hours: 24);

  // UI constants
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 12.0;

  // Storage keys
  static const String selectedCategoriesKey = 'selected_categories';
  static const String notificationEnabledKey = 'notification_enabled';
  static const String notificationTimeKey = 'notification_time';
  static const String notificationTimesKey = 'notification_times';
  static const String notificationCountKey = 'notification_count';
  static const String onboardingCompleteKey = 'onboarding_complete';
  static const String preferredLanguageKey = 'preferred_language';
  static const String darkModeEnabledKey = 'dark_mode_enabled';
  static const String lastQuoteDateKey = 'last_quote_date';
  static const String favoriteQuoteIdsKey = 'favorite_quote_ids';
}
