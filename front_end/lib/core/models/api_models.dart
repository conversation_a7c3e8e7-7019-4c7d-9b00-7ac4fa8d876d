/// API response wrapper
class ApiResponse<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  ApiResponse.success(this.data) : error = null, isSuccess = true;
  ApiResponse.error(this.error) : data = null, isSuccess = false;
}

/// Quote response from API
class QuoteResponse {
  final int id;
  final String text;
  final String author;
  final String? source;
  final int categoryId;
  final String category;
  final List<String> tags;
  final bool isFeatured;
  final double qualityScore;
  final String createdAt;
  final String updatedAt;

  QuoteResponse({
    required this.id,
    required this.text,
    required this.author,
    this.source,
    required this.categoryId,
    required this.category,
    this.tags = const [],
    this.isFeatured = false,
    this.qualityScore = 0.0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory QuoteResponse.fromJson(Map<String, dynamic> json) {
    return QuoteResponse(
      id: json['id'] as int,
      text: json['text'] as String,
      author: json['author'] as String,
      source: json['source'] as String?,
      categoryId: json['category_id'] as int,
      category: json['category'] as String? ?? 'General',
      tags: json['tags'] != null ? List<String>.from(json['tags']) : [],
      isFeatured: json['is_featured'] as bool? ?? false,
      qualityScore: (json['quality_score'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'source': source,
      'category_id': categoryId,
      'category': category,
      'tags': tags,
      'is_featured': isFeatured,
      'quality_score': qualityScore,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

/// Quotes response with pagination
class QuotesResponse {
  final List<QuoteResponse> quotes;
  final int totalCount;
  final int page;
  final int limit;
  final bool hasMore;

  QuotesResponse({
    required this.quotes,
    required this.totalCount,
    required this.page,
    required this.limit,
    required this.hasMore,
  });

  factory QuotesResponse.fromJson(Map<String, dynamic> json) {
    return QuotesResponse(
      quotes: (json['quotes'] as List<dynamic>)
          .map((item) => QuoteResponse.fromJson(item))
          .toList(),
      totalCount: json['total_count'] as int,
      page: json['page'] as int,
      limit: json['limit'] as int,
      hasMore: json['has_more'] as bool? ?? false,
    );
  }
}

/// Category response from API
class CategoryResponse {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String color;
  final String icon;
  final int quoteCount;
  final int sortOrder;
  final String updatedAt;

  CategoryResponse({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    required this.color,
    required this.icon,
    this.quoteCount = 0,
    this.sortOrder = 0,
    required this.updatedAt,
  });

  factory CategoryResponse.fromJson(Map<String, dynamic> json) {
    return CategoryResponse(
      id: json['id'] as int,
      name: json['name'] as String,
      slug: json['slug'] as String,
      description: json['description'] as String?,
      color: json['color'] as String? ?? '#4ECDC4',
      icon: json['icon'] as String? ?? '📝',
      quoteCount: json['quote_count'] as int? ?? 0,
      sortOrder: json['sort_order'] as int? ?? 0,
      updatedAt: json['updated_at'] as String,
    );
  }
}

/// Quote sync data for offline usage
class QuoteSync {
  final int id;
  final String text;
  final String author;
  final int categoryId;
  final String categoryName;
  final List<String> tags;
  final bool isFeatured;
  final double qualityScore;
  final String createdAt;
  final String updatedAt;

  QuoteSync({
    required this.id,
    required this.text,
    required this.author,
    required this.categoryId,
    required this.categoryName,
    this.tags = const [],
    this.isFeatured = false,
    this.qualityScore = 0.0,
    required this.createdAt,
    required this.updatedAt,
  });

  factory QuoteSync.fromJson(Map<String, dynamic> json) {
    return QuoteSync(
      id: json['id'] as int,
      text: json['text'] as String,
      author: json['author'] as String,
      categoryId: json['category_id'] as int,
      categoryName: json['category_name'] as String,
      tags: json['tags'] != null ? List<String>.from(json['tags']) : [],
      isFeatured: json['is_featured'] as bool? ?? false,
      qualityScore: (json['quality_score'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'category_id': categoryId,
      'category_name': categoryName,
      'tags': tags,
      'is_featured': isFeatured,
      'quality_score': qualityScore,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}

/// Sync response
class SyncResponse {
  final List<QuoteSync> quotes;
  final int totalCount;
  final String syncTimestamp;
  final bool hasMore;

  SyncResponse({
    required this.quotes,
    required this.totalCount,
    required this.syncTimestamp,
    this.hasMore = false,
  });

  factory SyncResponse.fromJson(Map<String, dynamic> json) {
    return SyncResponse(
      quotes: (json['quotes'] as List<dynamic>)
          .map((item) => QuoteSync.fromJson(item))
          .toList(),
      totalCount: json['total_count'] as int,
      syncTimestamp: json['sync_timestamp'] as String,
      hasMore: json['has_more'] as bool? ?? false,
    );
  }
}

/// Widget quote response
class WidgetQuoteResponse {
  final int id;
  final String text;
  final String author;
  final String category;
  final String categoryColor;

  WidgetQuoteResponse({
    required this.id,
    required this.text,
    required this.author,
    required this.category,
    this.categoryColor = '#4ECDC4',
  });

  factory WidgetQuoteResponse.fromJson(Map<String, dynamic> json) {
    return WidgetQuoteResponse(
      id: json['id'] as int,
      text: json['text'] as String,
      author: json['author'] as String,
      category: json['category'] as String,
      categoryColor: json['category_color'] as String? ?? '#4ECDC4',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'category': category,
      'category_color': categoryColor,
    };
  }
}
