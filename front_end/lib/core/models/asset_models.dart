import 'package:equatable/equatable.dart';

/// Enum for different asset categories
enum AssetCategory {
  backgrounds,
  motivational,
  nature,
  abstract,
  lifestyle,
  achievements,
  avatars,
}

/// Extension for AssetCategory to provide display names and descriptions
extension AssetCategoryExtension on AssetCategory {
  String get displayName {
    switch (this) {
      case AssetCategory.backgrounds:
        return 'Backgrounds';
      case AssetCategory.motivational:
        return 'Motivational';
      case AssetCategory.nature:
        return 'Nature';
      case AssetCategory.abstract:
        return 'Abstract';
      case AssetCategory.lifestyle:
        return 'Lifestyle';
      case AssetCategory.achievements:
        return 'Achievements';
      case AssetCategory.avatars:
        return 'Avatars';
    }
  }

  String get description {
    switch (this) {
      case AssetCategory.backgrounds:
        return 'Beautiful background images for quotes and content';
      case AssetCategory.motivational:
        return 'Inspiring and motivational imagery';
      case AssetCategory.nature:
        return 'Natural landscapes and scenery';
      case AssetCategory.abstract:
        return 'Abstract patterns and artistic designs';
      case AssetCategory.lifestyle:
        return 'Lifestyle and wellness imagery';
      case AssetCategory.achievements:
        return 'Achievement and success themed images';
      case AssetCategory.avatars:
        return 'Profile pictures and avatar images';
    }
  }

  String get iconName {
    switch (this) {
      case AssetCategory.backgrounds:
        return 'image';
      case AssetCategory.motivational:
        return 'trending_up';
      case AssetCategory.nature:
        return 'nature';
      case AssetCategory.abstract:
        return 'palette';
      case AssetCategory.lifestyle:
        return 'spa';
      case AssetCategory.achievements:
        return 'emoji_events';
      case AssetCategory.avatars:
        return 'account_circle';
    }
  }
}

/// Metadata for assets
class AssetMetadata extends Equatable {
  final String? title;
  final String? description;
  final List<String>? tags;
  final String? source;
  final String? license;
  final String? author;
  final String? authorUrl;
  final Map<String, dynamic>? customData;

  const AssetMetadata({
    this.title,
    this.description,
    this.tags,
    this.source,
    this.license,
    this.author,
    this.authorUrl,
    this.customData,
  });

  AssetMetadata copyWith({
    String? title,
    String? description,
    List<String>? tags,
    String? source,
    String? license,
    String? author,
    String? authorUrl,
    Map<String, dynamic>? customData,
  }) {
    return AssetMetadata(
      title: title ?? this.title,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      source: source ?? this.source,
      license: license ?? this.license,
      author: author ?? this.author,
      authorUrl: authorUrl ?? this.authorUrl,
      customData: customData ?? this.customData,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'tags': tags,
      'source': source,
      'license': license,
      'author': author,
      'authorUrl': authorUrl,
      'customData': customData,
    };
  }

  factory AssetMetadata.fromJson(Map<String, dynamic> json) {
    return AssetMetadata(
      title: json['title'] as String?,
      description: json['description'] as String?,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>(),
      source: json['source'] as String?,
      license: json['license'] as String?,
      author: json['author'] as String?,
      authorUrl: json['authorUrl'] as String?,
      customData: json['customData'] as Map<String, dynamic>?,
    );
  }

  @override
  List<Object?> get props => [
        title,
        description,
        tags,
        source,
        license,
        author,
        authorUrl,
        customData,
      ];
}

/// Local asset model
class LocalAsset extends Equatable {
  final String id;
  final AssetCategory category;
  final String localPath;
  final String fileName;
  final int fileSize;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final AssetMetadata? metadata;
  final bool isFavorite;
  final int usageCount;

  const LocalAsset({
    required this.id,
    required this.category,
    required this.localPath,
    required this.fileName,
    required this.fileSize,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
    this.isFavorite = false,
    this.usageCount = 0,
  });

  LocalAsset copyWith({
    String? id,
    AssetCategory? category,
    String? localPath,
    String? fileName,
    int? fileSize,
    DateTime? createdAt,
    DateTime? updatedAt,
    AssetMetadata? metadata,
    bool? isFavorite,
    int? usageCount,
  }) {
    return LocalAsset(
      id: id ?? this.id,
      category: category ?? this.category,
      localPath: localPath ?? this.localPath,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
      isFavorite: isFavorite ?? this.isFavorite,
      usageCount: usageCount ?? this.usageCount,
    );
  }

  /// Get file size in human readable format
  String get fileSizeFormatted {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Get file extension
  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }

  /// Check if asset is an image
  bool get isImage {
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(fileExtension);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category.name,
      'localPath': localPath,
      'fileName': fileName,
      'fileSize': fileSize,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata?.toJson(),
      'isFavorite': isFavorite,
      'usageCount': usageCount,
    };
  }

  factory LocalAsset.fromJson(Map<String, dynamic> json) {
    return LocalAsset(
      id: json['id'] as String,
      category: AssetCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => AssetCategory.backgrounds,
      ),
      localPath: json['localPath'] as String,
      fileName: json['fileName'] as String,
      fileSize: json['fileSize'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      metadata: json['metadata'] != null
          ? AssetMetadata.fromJson(json['metadata'] as Map<String, dynamic>)
          : null,
      isFavorite: json['isFavorite'] as bool? ?? false,
      usageCount: json['usageCount'] as int? ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        id,
        category,
        localPath,
        fileName,
        fileSize,
        createdAt,
        updatedAt,
        metadata,
        isFavorite,
        usageCount,
      ];
}

/// Asset collection model for organizing assets
class AssetCollection extends Equatable {
  final String id;
  final String name;
  final String? description;
  final List<String> assetIds;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? coverAssetId;
  final bool isDefault;

  const AssetCollection({
    required this.id,
    required this.name,
    this.description,
    required this.assetIds,
    required this.createdAt,
    this.updatedAt,
    this.coverAssetId,
    this.isDefault = false,
  });

  AssetCollection copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? assetIds,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? coverAssetId,
    bool? isDefault,
  }) {
    return AssetCollection(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      assetIds: assetIds ?? this.assetIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      coverAssetId: coverAssetId ?? this.coverAssetId,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'assetIds': assetIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'coverAssetId': coverAssetId,
      'isDefault': isDefault,
    };
  }

  factory AssetCollection.fromJson(Map<String, dynamic> json) {
    return AssetCollection(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      assetIds: (json['assetIds'] as List<dynamic>).cast<String>(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      coverAssetId: json['coverAssetId'] as String?,
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        assetIds,
        createdAt,
        updatedAt,
        coverAssetId,
        isDefault,
      ];
}
