import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../providers/preferences_provider.dart';

class NotificationCountWidget extends StatelessWidget {
  const NotificationCountWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PreferencesProvider>(
      builder: (context, preferencesProvider, child) {
        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
          children: [
            // Current selection display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.notifications_active,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '${preferencesProvider.notificationCount} notification${preferencesProvider.notificationCount == 1 ? '' : 's'} per day',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Slider
            Text(
              'Choose frequency:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),

            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Theme.of(context).colorScheme.primary,
                inactiveTrackColor: Theme.of(context).colorScheme.outline,
                thumbColor: Theme.of(context).colorScheme.primary,
                overlayColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                valueIndicatorColor: Theme.of(context).colorScheme.primary,
                valueIndicatorTextStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              child: Slider(
                value: preferencesProvider.notificationCount.toDouble(),
                min: AppConstants.minNotifications.toDouble(),
                max: AppConstants.maxNotifications.toDouble(),
                divisions: AppConstants.maxNotifications - AppConstants.minNotifications,
                label: preferencesProvider.notificationCount.toString(),
                onChanged: (value) {
                  preferencesProvider.setNotificationCount(value.round());
                },
              ),
            ),

            // Labels
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${AppConstants.minNotifications}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                  Text(
                    '${AppConstants.maxNotifications}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            LayoutBuilder(
              builder: (context, constraints) {
                // Set a base column width (e.g., 100) and calculate how many columns fit
                int crossAxisCount = (constraints.maxWidth / 120).floor().clamp(3, 6);

                return Wrap(
                  spacing: 10,
                  runSpacing: 10,
                  children: List.generate(AppConstants.maxNotifications, (index) {
                    final count = index + 1;
                    final isSelected = preferencesProvider.notificationCount == count;

                    return SizedBox(
                      width: constraints.maxWidth / crossAxisCount - 10,
                      child: AspectRatio(
                        aspectRatio: 1.1,
                        child: _NotificationCountOption(
                          count: count,
                          isSelected: isSelected,
                          onTap: () => preferencesProvider.setNotificationCount(count),
                        ),
                      ),
                    );
                  }),
                );
              },
            )
          ],
          ),
        );
      },
    );
  }
}

class _NotificationCountOption extends StatelessWidget {
  final int count;
  final bool isSelected;
  final VoidCallback onTap;

  const _NotificationCountOption({
    required this.count,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.surface,
            border: Border.all(
              color:
                  isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.outline,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                count.toString(),
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.primary,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                count == 1 ? 'per day' : 'per day',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected ? Theme.of(context).colorScheme.onPrimary : Colors.grey[600],
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
