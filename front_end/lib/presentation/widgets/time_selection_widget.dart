import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/utils/time_utils.dart';
import '../providers/preferences_provider.dart';

class TimeSelectionWidget extends StatelessWidget {
  const TimeSelectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PreferencesProvider>(
      builder: (context, preferencesProvider, child) {
        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Random times option
            Card(
              child: ListTile(
                leading: const Icon(Icons.shuffle),
                title: const Text('Use Random Times'),
                subtitle: const Text('We\'ll choose optimal times between 8 AM - 8 PM'),
                trailing: FilledButton.tonal(
                  onPressed: () => _generateRandomTimes(context, preferencesProvider),
                  child: const Text('Generate'),
                ),
              ),
            ),

            const SizedBox(height: 16),

            Text(
              'Or set specific times:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),

            const SizedBox(height: 12),

            // Time slots
            ...List.generate(
              preferencesProvider.notificationCount,
              (index) {
                final time = index < preferencesProvider.notificationTimes.length
                    ? preferencesProvider.notificationTimes[index]
                    : const TimeOfDay(hour: 9, minute: 0);

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _TimeSlotCard(
                    index: index,
                    time: time,
                    onTimeChanged: (newTime) {
                      preferencesProvider.setNotificationTime(index, newTime);
                    },
                  ),
                );
              },
            ),
          ],
          ),
        );
      },
    );
  }

  void _generateRandomTimes(BuildContext context, PreferencesProvider preferencesProvider) {
    try {
      final randomTimes = TimeUtils.generateRandomTimes(preferencesProvider.notificationCount);

      // Update all times at once for better performance
      preferencesProvider.setNotificationTimes(randomTimes);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Random notification times generated!'),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to generate times: $e'),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

class _TimeSlotCard extends StatelessWidget {
  final int index;
  final TimeOfDay time;
  final Function(TimeOfDay) onTimeChanged;

  const _TimeSlotCard({
    required this.index,
    required this.time,
    required this.onTimeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          child: Text(
            '${index + 1}',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text('Notification ${index + 1}'),
        subtitle: Text(_getTimeDescription(time)),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              TimeUtils.formatTimeOfDay(time),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () => _selectTime(context),
              icon: const Icon(Icons.access_time),
              tooltip: 'Change time',
            ),
          ],
        ),
      ),
    );
  }

  String _getTimeDescription(TimeOfDay time) {
    if (time.hour < 12) {
      return 'Morning motivation';
    } else if (time.hour < 17) {
      return 'Afternoon inspiration';
    } else {
      return 'Evening reflection';
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    try {
      final TimeOfDay? newTime = await showTimePicker(
        context: context,
        initialTime: time,
        helpText: 'Select notification time',
        cancelText: 'Cancel',
        confirmText: 'Set Time',
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              timePickerTheme: TimePickerThemeData(
                backgroundColor: Theme.of(context).colorScheme.surface,
                hourMinuteTextColor: Theme.of(context).colorScheme.primary,
                dayPeriodTextColor: Theme.of(context).colorScheme.primary,
                dialHandColor: Theme.of(context).colorScheme.primary,
                dialTextColor: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            child: child!,
          );
        },
      );

      if (newTime != null && newTime != time) {
        onTimeChanged(newTime);

        // Show confirmation
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Time updated to ${TimeUtils.formatTimeOfDay(newTime)}'),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update time: $e'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
