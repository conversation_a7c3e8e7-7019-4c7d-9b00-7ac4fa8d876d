import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shimmer/shimmer.dart'; 

enum LoadingType {
  dots,
  pulse,
  wave,
  shimmer,
  bounce,
  rotate,
}

class AnimatedLoadingWidget extends StatefulWidget {
  final LoadingType type;
  final String? message;
  final Color? color;
  final double size;
  final Duration duration;

  const AnimatedLoadingWidget({
    super.key,
    this.type = LoadingType.dots,
    this.message,
    this.color,
    this.size = 40.0,
    this.duration = const Duration(milliseconds: 1200),
  });

  @override
  State<AnimatedLoadingWidget> createState() => _AnimatedLoadingWidgetState();
}

class _AnimatedLoadingWidgetState extends State<AnimatedLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = widget.color ?? theme.colorScheme.primary;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: _buildLoadingAnimation(color),
        ),
        if (widget.message != null) ...[
          const SizedBox(height: 16),
          Text(
            widget.message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildLoadingAnimation(Color color) {
    switch (widget.type) {
      case LoadingType.dots:
        return _buildDotsAnimation(color);
      case LoadingType.pulse:
        return _buildPulseAnimation(color);
      case LoadingType.wave:
        return _buildWaveAnimation(color);
      case LoadingType.shimmer:
        return _buildShimmerAnimation(color);
      case LoadingType.bounce:
        return _buildBounceAnimation(color);
      case LoadingType.rotate:
        return _buildRotateAnimation(color);
    }
  }

  Widget _buildDotsAnimation(Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 2),
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          )
          .animate(delay: (index * 200).ms)
          .scaleXY(begin: 0.5, end: 1.5, duration: 400.ms)
          .then()
          .scaleXY(begin: 1.5, end: 0.5, duration: 400.ms),
        );
      }),
    );
  }

  Widget _buildPulseAnimation(Color color) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.5 + (_controller.value * 0.5),
          child: Container(
            decoration: BoxDecoration(
              color: color.withOpacity(0.3 + (_controller.value * 0.7)),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildWaveAnimation(Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(5, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 1),
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final animationValue = (_controller.value + (index * 0.1)) % 1.0;
              return Container(
                width: 4,
                height: 8 + (animationValue * 16),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildShimmerAnimation(Color color) {
    return Shimmer.fromColors(
      baseColor: color.withOpacity(0.3),
      highlightColor: color.withOpacity(0.8),
      child: Container(
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildBounceAnimation(Color color) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final bounceValue = (1 - (_controller.value * 2 - 1).abs());
        return Transform.translate(
          offset: Offset(0, -bounceValue * 10),
          child: Container(
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildRotateAnimation(Color color) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _controller.value * 2 * 3.14159,
          child: Container(
            decoration: BoxDecoration(
              gradient: SweepGradient(
                colors: [
                  color,
                  color.withOpacity(0.3),
                  color,
                ],
              ),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }
}

// Specialized loading widgets for different contexts
class QuoteLoadingWidget extends StatelessWidget {
  const QuoteLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AnimatedLoadingWidget(
              type: LoadingType.shimmer,
              size: 60,
            ),
            const SizedBox(height: 24),
            Text(
              'Finding the perfect quote for you...',
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'This might take a moment',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class OnboardingLoadingWidget extends StatelessWidget {
  final String message;
  
  const OnboardingLoadingWidget({
    super.key,
    this.message = 'Setting up your experience...',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const AnimatedLoadingWidget(
            type: LoadingType.pulse,
            size: 80,
          ),
          const SizedBox(height: 32),
          Text(
            message,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const AnimatedLoadingWidget(
            type: LoadingType.dots,
            size: 30,
          ),
        ],
      ),
    );
  }
}

class SettingsLoadingWidget extends StatelessWidget {
  const SettingsLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: AnimatedLoadingWidget(
        type: LoadingType.wave,
        message: 'Saving your preferences...',
        size: 50,
      ),
    );
  }
}
