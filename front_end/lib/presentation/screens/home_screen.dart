import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_theme.dart';
import '../providers/quote_provider.dart';
import '../providers/preferences_provider.dart';
import '../providers/analytics_provider.dart';
import '../providers/gamification_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/quote_card_widget.dart';
import '../widgets/animated_loading_widget.dart';
import '../../core/services/image_service.dart';
import 'settings_screen.dart';
import 'image_showcase_screen.dart';
import 'statistics_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _fabController;
  late AnimationController _cardController;
  late AnimationController _headerController;

  @override
  void initState() {
    super.initState();

    _fabController = AnimationController(
      duration: AppTheme.normalAnimation,
      vsync: this,
    );

    _cardController = AnimationController(
      duration: AppTheme.slowAnimation,
      vsync: this,
    );

    _headerController = AnimationController(
      duration: AppTheme.normalAnimation,
      vsync: this,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchInitialQuote();
      _startAnimations();
    });
  }

  void _startAnimations() {
    _headerController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _cardController.forward();
    });
    Future.delayed(const Duration(milliseconds: 400), () {
      _fabController.forward();
    });
  }

  Future<void> _fetchInitialQuote() async {
    final quoteProvider = Provider.of<QuoteProvider>(context, listen: false);
    final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
    await quoteProvider.fetchRandomQuote(preferencesProvider.selectedCategories);
  }

  @override
  void dispose() {
    _fabController.dispose();
    _cardController.dispose();
    _headerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface.withOpacity(0.95),
        elevation: 0,
        scrolledUnderElevation: 4,
        title: AnimatedBuilder(
          animation: _headerController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, -20 * (1 - _headerController.value)),
              child: Opacity(
                opacity: _headerController.value,
                child: const Text(
                  'Daily Motivator',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 24,
                  ),
                ),
              ),
            );
          },
        ),
        actions: [
          AnimatedBuilder(
            animation: _headerController,
            builder: (context, child) {
              return Transform.scale(
                scale: _headerController.value,
                child: Consumer<ThemeProvider>(
                  builder: (context, themeProvider, child) {
                    return IconButton(
                      icon: Icon(themeProvider.currentThemeModeIcon),
                      onPressed: () => themeProvider.toggleTheme(),
                      tooltip: 'Toggle theme',
                    );
                  },
                ),
              );
            },
          ),
          AnimatedBuilder(
            animation: _headerController,
            builder: (context, child) {
              return Transform.scale(
                scale: _headerController.value,
                child: IconButton(
                  icon: const Icon(Icons.settings),
                  onPressed: () {
                    Navigator.push(
                      context,
                      PageRouteBuilder(
                        pageBuilder: (context, animation, secondaryAnimation) =>
                            const SettingsScreen(),
                        transitionsBuilder: (context, animation, secondaryAnimation, child) {
                          return SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(1.0, 0.0),
                              end: Offset.zero,
                            ).animate(CurvedAnimation(
                              parent: animation,
                              curve: AppTheme.defaultCurve,
                            )),
                            child: child,
                          );
                        },
                        transitionDuration: AppTheme.normalAnimation,
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              customColors.primaryGradient.first.withOpacity(0.1),
              theme.colorScheme.surface,
            ],
          ),
        ),
        child: RefreshIndicator(
          onRefresh: () async {
            HapticFeedback.lightImpact();
            final quoteProvider = Provider.of<QuoteProvider>(context, listen: false);
            final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
            await quoteProvider.fetchRandomQuote(preferencesProvider.selectedCategories);
          },
          child: Consumer3<QuoteProvider, AnalyticsProvider, GamificationProvider>(
            builder: (context, quoteProvider, analyticsProvider, gamificationProvider, child) {
              return CustomScrollView(
                physics: const BouncingScrollPhysics(parent: AlwaysScrollableScrollPhysics()),
                slivers: [
                  SliverPadding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
                    sliver: SliverList(
                      delegate: SliverChildListDelegate([
                        _buildAnimatedWidget(
                          child: _buildGreetingCard(analyticsProvider, gamificationProvider),
                          delay: 0,
                        ),
                        const SizedBox(height: 24),
                        _buildAnimatedWidget(
                          child: _buildQuoteSection(quoteProvider),
                          delay: 200,
                        ),
                        const SizedBox(height: 24),
                        _buildAnimatedWidget(
                          child: _buildStatsRow(analyticsProvider, gamificationProvider),
                          delay: 400,
                        ),
                        const SizedBox(height: 24),
                        _buildAnimatedWidget(
                          child: _buildQuickActions(quoteProvider),
                          delay: 600,
                        ),
                        const SizedBox(height: 24),
                        _buildAnimatedWidget(
                          child: _buildCategoriesInfo(),
                          delay: 800,
                        ),
                      ]),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _fabController,
        builder: (context, child) {
          return Transform.scale(
            scale: _fabController.value,
            child: Transform.rotate(
              angle: _fabController.value * 0.5,
              child: Consumer<QuoteProvider>(
                builder: (context, quoteProvider, child) {
                  return FloatingActionButton.extended(
                    onPressed: quoteProvider.status == QuoteStatus.loading
                        ? null
                        : () {
                            HapticFeedback.mediumImpact();
                            final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
                            quoteProvider.fetchRandomQuote(preferencesProvider.selectedCategories);
                          },
                    icon: quoteProvider.status == QuoteStatus.loading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.refresh),
                    label: Text(quoteProvider.status == QuoteStatus.loading
                        ? 'Loading...'
                        : 'New Quote'),
                    backgroundColor: customColors.primaryGradient.first,
                    foregroundColor: Colors.white,
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnimatedWidget({required Widget child, required int delay}) {
    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, _) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _cardController.value)),
          child: Opacity(
            opacity: _cardController.value,
            child: child,
          ),
        );
      },
    );
  }

  Widget _buildGreetingCard(AnalyticsProvider analyticsProvider, GamificationProvider gamificationProvider) {
    final hour = DateTime.now().hour;
    String greeting;
    String emoji;

    if (hour < 12) {
      greeting = 'Good Morning!';
      emoji = '🌅';
    } else if (hour < 17) {
      greeting = 'Good Afternoon!';
      emoji = '☀️';
    } else {
      greeting = 'Good Evening!';
      emoji = '🌙';
    }

    final theme = Theme.of(context);
    final customColors = theme.customColors;

    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - _cardController.value)),
          child: Opacity(
            opacity: _cardController.value,
            child: Card(
              elevation: 8,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: customColors.motivationalGradient,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            emoji,
                            style: const TextStyle(fontSize: 32),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  greeting,
                                  style: theme.textTheme.headlineSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                Text(
                                  'Ready for your daily dose of motivation?',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: Colors.white.withOpacity(0.9),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          _buildStatChip(
                            '🔥 ${analyticsProvider.currentStreak}',
                            'Day Streak',
                          ),
                          const SizedBox(width: 12),
                          _buildStatChip(
                            '⭐ ${gamificationProvider.level}',
                            'Level',
                          ),
                          const SizedBox(width: 12),
                          _buildStatChip(
                            '📚 ${analyticsProvider.quotesTodayCount}',
                            'Today',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatChip(String value, String label) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuoteSection(QuoteProvider quoteProvider) {
    switch (quoteProvider.status) {
      case QuoteStatus.loading:
        return const QuoteLoadingWidget();

      case QuoteStatus.loaded:
        if (quoteProvider.currentQuote != null) {
          return AnimatedBuilder(
            animation: _cardController,
            builder: (context, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * _cardController.value),
                child: QuoteCardWidget(quote: quoteProvider.currentQuote!),
              );
            },
          );
        }
        return _buildErrorCard('No quote available');

      case QuoteStatus.error:
        return _buildErrorCard(
            quoteProvider.errorMessage.isNotEmpty ? quoteProvider.errorMessage : 'Failed to load quote');

      case QuoteStatus.initial:
      default:
        return Card(
          child: Container(
            padding: const EdgeInsets.all(32.0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primaryContainer,
                  Theme.of(context).colorScheme.secondaryContainer,
                ],
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.format_quote,
                  size: 48,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'Tap "New Quote" to get started!',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
    }
  }

  Widget _buildErrorCard(String message) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsRow(AnalyticsProvider analyticsProvider, GamificationProvider gamificationProvider) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Expanded(
          child: _buildStatsCard(
            title: 'Total Quotes',
            value: '${analyticsProvider.totalQuotesViewed}',
            icon: Icons.library_books,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatsCard(
            title: 'Current Streak',
            value: '${analyticsProvider.currentStreak}',
            icon: Icons.local_fire_department,
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatsCard(
            title: 'Level',
            value: '${gamificationProvider.level}',
            icon: Icons.star,
            color: Colors.amber,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(QuoteProvider quoteProvider) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.analytics,
                    label: 'Statistics',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const StatisticsScreen(),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.image,
                    label: 'Images',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ImageShowcaseScreen(),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.share,
                    label: 'Share',
                    onTap: () {
                      if (quoteProvider.currentQuote != null) {
                        // Share current quote
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.outline.withOpacity(0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: theme.colorScheme.primary,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesInfo() {
    return Consumer<PreferencesProvider>(
      builder: (context, preferencesProvider, child) {
        if (preferencesProvider.selectedCategories.isEmpty) {
          return Card(
            color: Colors.orange[50],
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.orange[700],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No categories selected',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.orange[700],
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Go to settings to select your favorite quote categories for personalized notifications.',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.orange[600],
                        ),
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Categories',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: preferencesProvider.selectedCategories
                      .map((category) => _buildCategoryChip(category))
                      .toList(),
                ),
                const SizedBox(height: 8),
                Text(
                  '${preferencesProvider.notificationCount} notification${preferencesProvider.notificationCount == 1 ? '' : 's'} per day',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryChip(String category) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            // Could navigate to category-specific quotes
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primaryContainer,
                  theme.colorScheme.secondaryContainer,
                ],
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ImageService.buildCategoryIcon(
                  category: category,
                  size: 24,
                  fallbackColor: theme.colorScheme.primary,
                  fallbackIcon: Icons.category,
                ),
                const SizedBox(width: 8),
                Text(
                  category.substring(0, 1).toUpperCase() + category.substring(1),
                  style: theme.textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
