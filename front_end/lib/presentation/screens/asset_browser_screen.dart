import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../core/models/asset_models.dart'; 
import '../../core/services/sharing_service.dart';
import '../widgets/asset_widgets.dart';
import '../providers/asset_provider.dart';

/// Screen for browsing and managing assets
class AssetBrowserScreen extends StatefulWidget {
  final AssetCategory? initialCategory;
  final Function(LocalAsset)? onAssetSelected;
  final bool selectionMode;

  const AssetBrowserScreen({
    super.key,
    this.initialCategory,
    this.onAssetSelected,
    this.selectionMode = false,
  });

  @override
  State<AssetBrowserScreen> createState() => _AssetBrowserScreenState();
}

class _AssetBrowserScreenState extends State<AssetBrowserScreen> {
  AssetCategory? _selectedCategory;
  String _searchQuery = '';
  final Set<String> _selectedAssets = {};
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.initialCategory;
    
    // Initialize asset provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AssetProvider>().loadAssets();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.selectionMode ? 'Select Image' : 'Image Gallery'),
        actions: [
          // Search button
          IconButton(
            onPressed: _showSearchDialog,
            icon: const Icon(Icons.search),
          ),
          
          // View toggle
          IconButton(
            onPressed: () => setState(() => _isGridView = !_isGridView),
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
          ),
          
          // Selection actions
          if (_selectedAssets.isNotEmpty) ...[
            IconButton(
              onPressed: _shareSelectedAssets,
              icon: const Icon(Icons.share),
            ),
            IconButton(
              onPressed: _deleteSelectedAssets,
              icon: const Icon(Icons.delete),
            ),
          ],
          
          // Menu
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'download',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Download More Images'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: ListTile(
                  leading: Icon(Icons.refresh),
                  title: Text('Refresh Gallery'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('Gallery Settings'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      
      body: Column(
        children: [
          // Category selector
          CategorySelector(
            selectedCategory: _selectedCategory,
            onCategoryChanged: (category) {
              setState(() {
                _selectedCategory = category;
                _selectedAssets.clear();
              });
            },
          ),
          
          // Search bar (if searching)
          if (_searchQuery.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Search results for "$_searchQuery"',
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                  TextButton(
                    onPressed: () => setState(() => _searchQuery = ''),
                    child: const Text('Clear'),
                  ),
                ],
              ),
            ),
          
          // Asset grid/list
          Expanded(
            child: Consumer<AssetProvider>(
              builder: (context, assetProvider, child) {
                if (assetProvider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                if (assetProvider.error != null) {
                  return _buildErrorView(assetProvider.error!);
                }
                
                final assets = _getFilteredAssets(assetProvider.assets);
                
                if (_isGridView) {
                  return AssetGrid(
                    assets: assets,
                    onAssetTap: _handleAssetTap,
                    onAssetFavorite: _toggleAssetFavorite,
                    onAssetShare: _shareAsset,
                    onAssetDelete: _deleteAsset,
                    selectedAssetIds: _selectedAssets,
                    showActions: !widget.selectionMode,
                    crossAxisCount: _getCrossAxisCount(context),
                  );
                } else {
                  return _buildListView(assets);
                }
              },
            ),
          ),
        ],
      ),
      
      // Floating action button for adding custom images
      floatingActionButton: widget.selectionMode ? null : FloatingActionButton(
        onPressed: _addCustomImage,
        child: const Icon(Icons.add_photo_alternate),
      ),
    );
  }

  List<LocalAsset> _getFilteredAssets(Map<AssetCategory, List<LocalAsset>> allAssets) {
    List<LocalAsset> assets = [];
    
    if (_selectedCategory != null) {
      assets = allAssets[_selectedCategory] ?? [];
    } else {
      // Show all assets
      for (final categoryAssets in allAssets.values) {
        assets.addAll(categoryAssets);
      }
    }
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      assets = assets.where((asset) {
        final query = _searchQuery.toLowerCase();
        return asset.fileName.toLowerCase().contains(query) ||
               asset.metadata?.title?.toLowerCase().contains(query) == true ||
               asset.metadata?.tags?.any((tag) => tag.toLowerCase().contains(query)) == true;
      }).toList();
    }
    
    // Sort by creation date (newest first)
    assets.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return assets;
  }

  Widget _buildListView(List<LocalAsset> assets) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: assets.length,
      itemBuilder: (context, index) {
        final asset = assets[index];
        final isSelected = _selectedAssets.contains(asset.id);
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(asset.localPath),
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 60,
                    height: 60,
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Icon(Icons.broken_image),
                  );
                },
              ),
            ),
            title: Text(asset.metadata?.title ?? asset.fileName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(asset.category.displayName),
                Text(
                  '${asset.fileSizeFormatted} • ${asset.createdAt.toString().split(' ')[0]}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            trailing: widget.selectionMode
                ? Checkbox(
                    value: isSelected,
                    onChanged: (value) => _toggleAssetSelection(asset.id),
                  )
                : PopupMenuButton<String>(
                    onSelected: (action) => _handleAssetAction(action, asset),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'favorite',
                        child: ListTile(
                          leading: Icon(
                            asset.isFavorite ? Icons.favorite : Icons.favorite_border,
                          ),
                          title: Text(asset.isFavorite ? 'Unfavorite' : 'Favorite'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: ListTile(
                          leading: Icon(Icons.share),
                          title: Text('Share'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete),
                          title: Text('Delete'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
            onTap: () => _handleAssetTap(asset),
            selected: isSelected,
          ),
        ).animate().fadeIn(duration: 300.ms);
      },
    );
  }

  Widget _buildErrorView(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Images',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.read<AssetProvider>().loadAssets(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 4;
    if (width > 800) return 3;
    return 2;
  }

  void _handleAssetTap(LocalAsset asset) {
    if (widget.selectionMode) {
      widget.onAssetSelected?.call(asset);
      Navigator.of(context).pop(asset);
    } else if (_selectedAssets.isNotEmpty) {
      _toggleAssetSelection(asset.id);
    } else {
      // Show asset details
      _showAssetDetails(asset);
    }
  }

  void _toggleAssetSelection(String assetId) {
    setState(() {
      if (_selectedAssets.contains(assetId)) {
        _selectedAssets.remove(assetId);
      } else {
        _selectedAssets.add(assetId);
      }
    });
  }

  void _toggleAssetFavorite(LocalAsset asset) {
    context.read<AssetProvider>().toggleFavorite(asset);
  }

  void _shareAsset(LocalAsset asset) {
    SharingService.shareImage(asset);
  }

  void _deleteAsset(LocalAsset asset) {
    _showDeleteConfirmation([asset]);
  }

  void _shareSelectedAssets() {
    final provider = context.read<AssetProvider>();
    final assets = _selectedAssets
        .map((id) => provider.getAssetById(id))
        .where((asset) => asset != null)
        .cast<LocalAsset>()
        .toList();
    
    if (assets.isNotEmpty) {
      SharingService.shareAssetCollection(assets);
    }
  }

  void _deleteSelectedAssets() {
    final provider = context.read<AssetProvider>();
    final assets = _selectedAssets
        .map((id) => provider.getAssetById(id))
        .where((asset) => asset != null)
        .cast<LocalAsset>()
        .toList();
    
    if (assets.isNotEmpty) {
      _showDeleteConfirmation(assets);
    }
  }

  void _showDeleteConfirmation(List<LocalAsset> assets) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete ${assets.length == 1 ? 'Image' : 'Images'}'),
        content: Text(
          assets.length == 1
              ? 'Are you sure you want to delete this image?'
              : 'Are you sure you want to delete ${assets.length} images?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              for (final asset in assets) {
                context.read<AssetProvider>().deleteAsset(asset);
              }
              setState(() => _selectedAssets.clear());
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Images'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Enter search terms...',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (query) {
            Navigator.of(context).pop();
            setState(() => _searchQuery = query.trim());
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showAssetDetails(LocalAsset asset) {
    // Navigate to asset details screen
    // Implementation would depend on your navigation setup
  }

  void _addCustomImage() {
    // Implementation for adding custom images
    // Would typically use image_picker package
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'download':
        context.read<AssetProvider>().downloadDefaultAssets();
        break;
      case 'refresh':
        context.read<AssetProvider>().loadAssets();
        break;
      case 'settings':
        // Navigate to gallery settings
        break;
    }
  }

  void _handleAssetAction(String action, LocalAsset asset) {
    switch (action) {
      case 'favorite':
        _toggleAssetFavorite(asset);
        break;
      case 'share':
        _shareAsset(asset);
        break;
      case 'delete':
        _deleteAsset(asset);
        break;
    }
  }
}
