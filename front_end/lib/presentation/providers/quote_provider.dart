import 'package:flutter/foundation.dart';
import '../../domain/entities/quote.dart';
import '../../domain/usecases/get_random_quote.dart';

enum QuoteStatus {
  initial,
  loading,
  loaded,
  error,
}

class QuoteProvider extends ChangeNotifier {
  final GetRandomQuote getRandomQuote;

  QuoteProvider({required this.getRandomQuote});

  QuoteStatus _status = QuoteStatus.initial;
  Quote? _currentQuote;
  String _errorMessage = '';
  List<Quote> _quoteHistory = [];

  // Getters
  QuoteStatus get status => _status;
  Quote? get currentQuote => _currentQuote;
  String get errorMessage => _errorMessage;
  List<Quote> get quoteHistory => List.unmodifiable(_quoteHistory);
  bool get hasQuote => _currentQuote != null;
  bool get isLoading => _status == QuoteStatus.loading;

  // Fetch a random quote based on selected categories
  Future<void> fetchRandomQuote(List<String> categories) async {
    if (categories.isEmpty) {
      _setError('Please select at least one category');
      return;
    }

    _setLoading();

    try {
      final result = await getRandomQuote(
        GetRandomQuoteParams(categories: categories),
      );

      result.fold(
        (failure) => _setError(failure.message),
        (quote) => _setQuote(quote),
      );
    } catch (e) {
      _setError('An unexpected error occurred: ${e.toString()}');
    }
  }

  // Refresh current quote with same categories
  Future<void> refreshQuote(List<String> categories) async {
    await fetchRandomQuote(categories);
  }

  // Add current quote to history
  void addToHistory() {
    if (_currentQuote != null && !_quoteHistory.contains(_currentQuote)) {
      _quoteHistory.insert(0, _currentQuote!);
      // Keep only last 50 quotes in history
      if (_quoteHistory.length > 50) {
        _quoteHistory = _quoteHistory.take(50).toList();
      }
      notifyListeners();
    }
  }

  // Clear quote history
  void clearHistory() {
    _quoteHistory.clear();
    notifyListeners();
  }

  // Get a specific quote from history
  Quote? getQuoteFromHistory(int index) {
    if (index >= 0 && index < _quoteHistory.length) {
      return _quoteHistory[index];
    }
    return null;
  }

  // Set current quote from history
  void setQuoteFromHistory(int index) {
    final quote = getQuoteFromHistory(index);
    if (quote != null) {
      _currentQuote = quote;
      _status = QuoteStatus.loaded;
      _errorMessage = '';
      notifyListeners();
    }
  }

  // Check if a quote is in favorites (placeholder implementation)
  bool isQuoteFavorite(Quote quote) {
    // This would typically check against saved favorites
    return false;
  }

  // Toggle favorite status (placeholder implementation)
  Future<void> toggleFavorite(Quote quote) async {
    // This would typically save/remove from favorites
    // For now, just notify listeners
    notifyListeners();
  }

  // Share quote text
  String getShareText(Quote quote) {
    return '"${quote.text}"\n\n- ${quote.author}';
  }

  // Get quote statistics
  Map<String, int> getQuoteStatistics() {
    final stats = <String, int>{};

    for (final quote in _quoteHistory) {
      stats[quote.category] = (stats[quote.category] ?? 0) + 1;
    }

    return stats;
  }

  // Get most viewed category
  String? getMostViewedCategory() {
    final stats = getQuoteStatistics();
    if (stats.isEmpty) return null;

    return stats.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  // Private helper methods
  void _setLoading() {
    _status = QuoteStatus.loading;
    _errorMessage = '';
    notifyListeners();
  }

  void _setQuote(Quote quote) {
    _currentQuote = quote;
    _status = QuoteStatus.loaded;
    _errorMessage = '';
    addToHistory();
    notifyListeners();
  }

  void _setError(String message) {
    _status = QuoteStatus.error;
    _errorMessage = message;
    notifyListeners();
  }

  // Reset provider state
  void reset() {
    _status = QuoteStatus.initial;
    _currentQuote = null;
    _errorMessage = '';
    _quoteHistory.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _quoteHistory.clear();
    super.dispose();
  }
}
