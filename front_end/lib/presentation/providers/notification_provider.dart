import 'package:flutter/material.dart';
import '../../core/services/notification_service.dart';
import '../../core/utils/linux_notification_helper.dart';
import '../../core/utils/platform_utils.dart';
import '../../domain/usecases/get_random_quote.dart';

class NotificationProvider extends ChangeNotifier {
  final NotificationService notificationService;
  final GetRandomQuote getRandomQuote;

  bool _permissionsGranted = false;
  bool _isScheduling = false;

  NotificationProvider({
    required this.notificationService,
    required this.getRandomQuote,
  });

  bool get permissionsGranted => _permissionsGranted;
  bool get isScheduling => _isScheduling;

  Future<void> initialize() async {
    await notificationService.initialize();
  }

  Future<bool> requestPermissions() async {
    try {
      _permissionsGranted = await notificationService.requestPermissions();
      notifyListeners();
      return _permissionsGranted;
    } catch (e) {
      // On Linux, provide helpful feedback about notification setup
      if (PlatformUtils.isLinux) {
        debugPrint('Linux notification permission issue: $e');
        // Still return true to allow app functionality
        _permissionsGranted = true;
        notifyListeners();
        return true;
      }
      rethrow;
    }
  }

  // Check notification daemon status (Linux-specific)
  Future<NotificationStatus> checkNotificationStatus() async {
    return await LinuxNotificationHelper.getNotificationStatus();
  }

  // Test notification functionality
  Future<bool> testNotification() async {
    if (PlatformUtils.isLinux) {
      return await LinuxNotificationHelper.testNotification();
    }
    // For other platforms, assume notifications work if permissions are granted
    return _permissionsGranted;
  }

  // Show notification setup dialog
  void showNotificationSetupDialog(BuildContext context) async {
    final status = await checkNotificationStatus();
    if (context.mounted) {
      LinuxNotificationHelper.showNotificationSetupDialog(context, status);
    }
  }

  Future<void> scheduleNotifications(List<TimeOfDay> times) async {
    if (!_permissionsGranted) {
      throw Exception('Notification permissions not granted');
    }

    _isScheduling = true;
    notifyListeners();

    try {
      // Schedule notifications with categories
      await notificationService.scheduleQuoteNotifications(
        times: times,
        categories: ['motivation', 'success', 'inspiration'], // Default categories
      );
    } catch (e) {
      throw Exception('Failed to schedule notifications: $e');
    } finally {
      _isScheduling = false;
      notifyListeners();
    }
  }
}
