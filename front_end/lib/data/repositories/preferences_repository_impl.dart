import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../domain/entities/user_preferences.dart';
import '../../domain/repositories/preferences_repository.dart';
import '../datasources/preferences_local_datasource.dart';
import '../models/user_preferences_model.dart';

class PreferencesRepositoryImpl implements PreferencesRepository {
  final PreferencesLocalDataSource localDataSource;

  PreferencesRepositoryImpl({required this.localDataSource});

  @override
  Future<Either<Failure, UserPreferences>> getUserPreferences() async {
    try {
      final preferences = await localDataSource.getUserPreferences();
      return Right(preferences);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> saveUserPreferences(UserPreferences preferences) async {
    try {
      final preferencesModel = UserPreferencesModel.fromEntity(preferences);
      await localDataSource.saveUserPreferences(preferencesModel);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateSelectedCategories(List<String> categories) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        selectedCategories: categories,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> toggleNotifications(bool enabled) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        notificationEnabled: enabled,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateNotificationTimes(List<TimeOfDay> times) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        notificationTimes: times,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateNotificationCount(int count) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        notificationCount: count,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> completeOnboarding() async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        onboardingComplete: true,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updatePreferredLanguage(String language) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        preferredLanguage: language,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> toggleDarkMode(bool enabled) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        darkModeEnabled: enabled,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateLastQuoteDate(DateTime date) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedPreferences = currentPreferences.copyWith(
        lastQuoteDate: date,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> addFavoriteQuote(String quoteId) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedFavorites = List<String>.from(currentPreferences.favoriteQuoteIds);
      if (!updatedFavorites.contains(quoteId)) {
        updatedFavorites.add(quoteId);
      }
      final updatedPreferences = currentPreferences.copyWith(
        favoriteQuoteIds: updatedFavorites,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> removeFavoriteQuote(String quoteId) async {
    try {
      final currentPreferences = await localDataSource.getUserPreferences();
      final updatedFavorites = List<String>.from(currentPreferences.favoriteQuoteIds);
      updatedFavorites.remove(quoteId);
      final updatedPreferences = currentPreferences.copyWith(
        favoriteQuoteIds: updatedFavorites,
      );
      await localDataSource.saveUserPreferences(updatedPreferences);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> clearPreferences() async {
    try {
      await localDataSource.clearPreferences();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> isOnboardingComplete() async {
    try {
      final preferences = await localDataSource.getUserPreferences();
      return Right(preferences.onboardingComplete);
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message, code: e.code));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}
