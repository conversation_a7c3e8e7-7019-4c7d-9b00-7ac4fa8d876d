import 'package:flutter/material.dart';
import '../../domain/entities/user_preferences.dart';

class UserPreferencesModel extends UserPreferences {
  const UserPreferencesModel({
    super.selectedCategories = const [],
    super.notificationEnabled = true,
    super.notificationTimes = const [TimeOfDay(hour: 9, minute: 0)],
    super.notificationCount = 3,
    super.onboardingComplete = false,
    super.preferredLanguage = 'en',
    super.darkModeEnabled = false,
    required super.lastQuoteDate,
    super.favoriteQuoteIds = const [],
  });

  factory UserPreferencesModel.fromEntity(UserPreferences preferences) {
    return UserPreferencesModel(
      selectedCategories: preferences.selectedCategories,
      notificationEnabled: preferences.notificationEnabled,
      notificationTimes: preferences.notificationTimes,
      notificationCount: preferences.notificationCount,
      onboardingComplete: preferences.onboardingComplete,
      preferredLanguage: preferences.preferredLanguage,
      darkModeEnabled: preferences.darkModeEnabled,
      lastQuoteDate: preferences.lastQuoteDate,
      favoriteQuoteIds: preferences.favoriteQuoteIds,
    );
  }

  @override
  UserPreferencesModel copyWith({
    List<String>? selectedCategories,
    bool? notificationEnabled,
    List<TimeOfDay>? notificationTimes,
    int? notificationCount,
    bool? onboardingComplete,
    String? preferredLanguage,
    bool? darkModeEnabled,
    DateTime? lastQuoteDate,
    List<String>? favoriteQuoteIds,
  }) {
    return UserPreferencesModel(
      selectedCategories: selectedCategories ?? this.selectedCategories,
      notificationEnabled: notificationEnabled ?? this.notificationEnabled,
      notificationTimes: notificationTimes ?? this.notificationTimes,
      notificationCount: notificationCount ?? this.notificationCount,
      onboardingComplete: onboardingComplete ?? this.onboardingComplete,
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      darkModeEnabled: darkModeEnabled ?? this.darkModeEnabled,
      lastQuoteDate: lastQuoteDate ?? this.lastQuoteDate,
      favoriteQuoteIds: favoriteQuoteIds ?? this.favoriteQuoteIds,
    );
  }

  // Factory for creating default preferences
  factory UserPreferencesModel.defaultPreferences() {
    return UserPreferencesModel(
      selectedCategories: const ['motivation', 'success', 'inspiration'],
      notificationEnabled: true,
      notificationTimes: const [
        TimeOfDay(hour: 9, minute: 0),
        TimeOfDay(hour: 14, minute: 0),
        TimeOfDay(hour: 18, minute: 0),
      ],
      notificationCount: 3,
      onboardingComplete: false,
      preferredLanguage: 'en',
      darkModeEnabled: false,
      lastQuoteDate: DateTime.now(),
      favoriteQuoteIds: const [],
    );
  }
}
