import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/exceptions.dart';
import '../models/user_preferences_model.dart';

abstract class PreferencesLocalDataSource {
  Future<UserPreferencesModel> getUserPreferences();
  Future<void> saveUserPreferences(UserPreferencesModel preferences);
  Future<void> clearPreferences();
}

class PreferencesLocalDataSourceImpl implements PreferencesLocalDataSource {
  final SharedPreferences sharedPreferences;

  PreferencesLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<UserPreferencesModel> getUserPreferences() async {
    try {
      final selectedCategories = sharedPreferences.getStringList(
            AppConstants.selectedCategoriesKey,
          ) ??
          AppConstants.availableCategories.take(3).toList();

      final notificationEnabled = sharedPreferences.getBool(
            AppConstants.notificationEnabledKey,
          ) ??
          true;

      final notificationTimesJson = sharedPreferences.getStringList(
            AppConstants.notificationTimesKey,
          ) ??
          ['9:0'];

      final notificationTimes = notificationTimesJson.map((timeString) {
        final parts = timeString.split(':');
        return TimeOfDay(
          hour: int.tryParse(parts[0]) ?? 9,
          minute: int.tryParse(parts[1]) ?? 0,
        );
      }).toList();

      final notificationCount = sharedPreferences.getInt(
            AppConstants.notificationCountKey,
          ) ??
          AppConstants.defaultNotificationCount;

      final onboardingComplete = sharedPreferences.getBool(
            AppConstants.onboardingCompleteKey,
          ) ??
          false;

      final preferredLanguage = sharedPreferences.getString(
            AppConstants.preferredLanguageKey,
          ) ??
          'en';

      final darkModeEnabled = sharedPreferences.getBool(
            AppConstants.darkModeEnabledKey,
          ) ??
          false;

      final lastQuoteDateString = sharedPreferences.getString(
        AppConstants.lastQuoteDateKey,
      );
      final lastQuoteDate = lastQuoteDateString != null
          ? DateTime.tryParse(lastQuoteDateString) ?? DateTime.now()
          : DateTime.now();

      final favoriteQuoteIds = sharedPreferences.getStringList(
            AppConstants.favoriteQuoteIdsKey,
          ) ??
          [];

      return UserPreferencesModel(
        selectedCategories: selectedCategories,
        notificationEnabled: notificationEnabled,
        notificationTimes: notificationTimes,
        notificationCount: notificationCount,
        onboardingComplete: onboardingComplete,
        preferredLanguage: preferredLanguage,
        darkModeEnabled: darkModeEnabled,
        lastQuoteDate: lastQuoteDate,
        favoriteQuoteIds: favoriteQuoteIds,
      );
    } catch (e) {
      throw CacheException(
        message: 'Failed to load user preferences: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> saveUserPreferences(UserPreferencesModel preferences) async {
    try {
      await Future.wait([
        sharedPreferences.setStringList(
          AppConstants.selectedCategoriesKey,
          preferences.selectedCategories,
        ),
        sharedPreferences.setBool(
          AppConstants.notificationEnabledKey,
          preferences.notificationEnabled,
        ),
        sharedPreferences.setStringList(
          AppConstants.notificationTimesKey,
          preferences.notificationTimes.map((time) => '${time.hour}:${time.minute}').toList(),
        ),
        sharedPreferences.setInt(
          AppConstants.notificationCountKey,
          preferences.notificationCount,
        ),
        sharedPreferences.setBool(
          AppConstants.onboardingCompleteKey,
          preferences.onboardingComplete,
        ),
        sharedPreferences.setString(
          AppConstants.preferredLanguageKey,
          preferences.preferredLanguage,
        ),
        sharedPreferences.setBool(
          AppConstants.darkModeEnabledKey,
          preferences.darkModeEnabled,
        ),
        sharedPreferences.setString(
          AppConstants.lastQuoteDateKey,
          preferences.lastQuoteDate.toIso8601String(),
        ),
        sharedPreferences.setStringList(
          AppConstants.favoriteQuoteIdsKey,
          preferences.favoriteQuoteIds,
        ),
      ]);
    } catch (e) {
      throw CacheException(
        message: 'Failed to save user preferences: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> clearPreferences() async {
    try {
      await Future.wait([
        sharedPreferences.remove(AppConstants.selectedCategoriesKey),
        sharedPreferences.remove(AppConstants.notificationEnabledKey),
        sharedPreferences.remove(AppConstants.notificationTimesKey),
        sharedPreferences.remove(AppConstants.notificationCountKey),
        sharedPreferences.remove(AppConstants.onboardingCompleteKey),
        sharedPreferences.remove(AppConstants.preferredLanguageKey),
        sharedPreferences.remove(AppConstants.darkModeEnabledKey),
        sharedPreferences.remove(AppConstants.lastQuoteDateKey),
        sharedPreferences.remove(AppConstants.favoriteQuoteIdsKey),
      ]);
    } catch (e) {
      throw CacheException(
        message: 'Failed to clear preferences: ${e.toString()}',
      );
    }
  }
}
