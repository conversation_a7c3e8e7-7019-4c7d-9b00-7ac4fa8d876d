#!/bin/bash

echo "🧪 Running Daily Motivator Tests"
echo "================================="

# Generate mocks
echo "📦 Generating mocks..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run all tests
echo "🚀 Running unit tests..."
flutter test --coverage

# Generate coverage report (if lcov is installed)
if command -v lcov &> /dev/null; then
    echo "📊 Generating coverage report..."
    genhtml coverage/lcov.info -o coverage/html
    echo "Coverage report generated at coverage/html/index.html"
fi

echo "✅ Tests completed!"
