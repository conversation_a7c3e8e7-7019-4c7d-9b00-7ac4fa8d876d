package com.example.daily_motivator

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.graphics.Color
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin

class DailyMotivatorWidget : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_REFRESH_QUOTE -> {
                // Trigger quote refresh
                val appWidgetManager = AppWidgetManager.getInstance(context)
                val appWidgetIds = appWidgetManager.getAppWidgetIds(
                    android.content.ComponentName(context, DailyMotivatorWidget::class.java)
                )
                
                // Request new quote from Flutter app
                val refreshIntent = Intent(context, MainActivity::class.java).apply {
                    action = "REFRESH_WIDGET_QUOTE"
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(refreshIntent)
                
                // Update widgets
                for (appWidgetId in appWidgetIds) {
                    updateAppWidget(context, appWidgetManager, appWidgetId)
                }
            }
        }
    }

    companion object {
        private const val ACTION_REFRESH_QUOTE = "com.example.daily_motivator.REFRESH_QUOTE"
        
        fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            // Get widget data from shared preferences
            val widgetData = HomeWidgetPlugin.getData(context)
            
            val quoteText = widgetData.getString("widget_quote_text", "Tap to refresh for a new quote")
            val quoteAuthor = widgetData.getString("widget_quote_author", "")
            val category = widgetData.getString("widget_quote_category", "Motivation")
            val categoryColor = widgetData.getString("widget_category_color", "#4ECDC4")
            
            // Create RemoteViews
            val views = RemoteViews(context.packageName, R.layout.daily_motivator_widget)
            
            // Set quote text
            views.setTextViewText(R.id.widget_quote_text, quoteText)
            
            // Set author (if available)
            if (quoteAuthor.isNotEmpty()) {
                views.setTextViewText(R.id.widget_quote_author, "— $quoteAuthor")
            } else {
                views.setTextViewText(R.id.widget_quote_author, "")
            }
            
            // Set category
            views.setTextViewText(R.id.widget_category, category)
            
            // Set category color
            try {
                val color = Color.parseColor(categoryColor)
                views.setInt(R.id.widget_category, "setBackgroundColor", color)
            } catch (e: Exception) {
                // Fallback to default color
                views.setInt(R.id.widget_category, "setBackgroundColor", Color.parseColor("#4ECDC4"))
            }
            
            // Set up refresh button click
            val refreshIntent = Intent(context, DailyMotivatorWidget::class.java).apply {
                action = ACTION_REFRESH_QUOTE
            }
            val refreshPendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                refreshIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_refresh_button, refreshPendingIntent)
            
            // Set up widget click to open app
            val openAppIntent = Intent(context, MainActivity::class.java)
            val openAppPendingIntent = PendingIntent.getActivity(
                context,
                0,
                openAppIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_quote_text, openAppPendingIntent)
            
            // Update the widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
    }
}
