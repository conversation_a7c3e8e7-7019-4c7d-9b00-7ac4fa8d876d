<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Category indicator -->
    <TextView
        android:id="@+id/widget_category"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/category_badge"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="Motivation"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        android:textStyle="bold" />

    <!-- Quote text -->
    <TextView
        android:id="@+id/widget_quote_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="4"
        android:text="The only way to do great work is to love what you do."
        android:textColor="#333333"
        android:textSize="14sp"
        android:textStyle="normal" />

    <!-- Author and refresh button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/widget_quote_author"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="— Steve Jobs"
            android:textColor="#666666"
            android:textSize="12sp"
            android:textStyle="italic" />

        <ImageButton
            android:id="@+id/widget_refresh_button"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/refresh_button_background"
            android:contentDescription="@string/refresh_quote"
            android:scaleType="centerInside"
            android:src="@drawable/ic_refresh" />

    </LinearLayout>

</LinearLayout>
