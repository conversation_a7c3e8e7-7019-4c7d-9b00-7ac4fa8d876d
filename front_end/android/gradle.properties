org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Speed up builds
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.configureondemand=true
org.gradle.caching=true
android.enableBuildCache=true
android.enableR8.fullMode=false
org.gradle.java.home=/usr/lib/jvm/java-17-openjdk-amd64

# Network and build optimization
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Network timeout settings
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
systemProp.https.connectionTimeout=300000
systemProp.https.socketTimeout=300000
