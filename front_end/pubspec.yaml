name: daily_motivator
description: Daily motivational quotes with customizable notifications
publish_to: "none"
version: 1.0.0+2

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.6
  http: ^1.2.0
  provider: ^6.1.1
  dartz: ^0.10.1
  equatable: ^2.0.5
  get_it: ^7.6.7

  # Notifications & Permissions
  flutter_local_notifications: ^17.0.0
  timezone: ^0.9.2
  flutter_native_timezone: ^2.0.0
  permission_handler: ^11.3.0

  # Storage & Preferences
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Utilities
  intl: ^0.19.0
  url_launcher: ^6.2.4
  path_provider: ^2.1.2

  # UI & Theming
  flutter_launcher_icons: ^0.13.1
  google_fonts: ^6.1.0
  flex_color_scheme: ^7.3.1

  # Animations & Visual Effects
  flutter_animate: ^4.5.0
  lottie: ^3.1.0
  # rive: ^0.12.4 
  shimmer: ^3.0.0
  confetti: ^0.7.0

  # Charts & Data Visualization
  fl_chart: ^0.66.2 

  # Advanced UI Components
  flutter_slidable: ^3.0.1
  card_swiper: ^3.0.1
  flutter_card_swiper: ^7.0.1
  pull_to_refresh: ^2.0.0
  liquid_pull_to_refresh: ^3.0.1

  # Image & Media
  cached_network_image: ^3.3.1
  image_picker: ^1.0.7

  # Social & Sharing
  share_plus: ^7.2.2
  social_share: ^2.3.1

  # Authentication
  google_sign_in: ^6.2.1
 

  # Widget Support
  home_widget: ^0.4.1
  workmanager: ^0.5.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  mockito: ^5.4.4
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1


flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/backgrounds/
    - assets/images/categories/
    - assets/images/achievements/
    - assets/images/avatars/
    - assets/images/motivational/
    - assets/images/nature/
    - assets/images/abstract/
    - assets/images/lifestyle/
    - assets/animations/
    - assets/lottie/
    - assets/rive/
    - assets/icons/



flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"
  adaptive_icon_background: "#FFFFFF"
