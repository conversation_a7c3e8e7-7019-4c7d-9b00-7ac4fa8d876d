#!/bin/bash

# === Config ===
GRADLE_VERSION="8.5"
GRADLE_DIST_URL="https://services.gradle.org/distributions/gradle-${GRADLE_VERSION}-all.zip"
GRADLE_DIR="$HOME/.gradle/wrapper/dists/gradle-${GRADLE_VERSION}-all"

echo "🔽 Downloading Gradle ${GRADLE_VERSION}..."
wget -c "$GRADLE_DIST_URL" -O "gradle-${GRADLE_VERSION}-all.zip"

# Check if Grad<PERSON> already extracted
if [ -d "$GRADLE_DIR" ]; then
  echo "📁 Gradle directory already exists: $GRADLE_DIR"
else
  echo "📁 Creating Gradle directory..."
  mkdir -p "$GRADLE_DIR/temp-folder"

  echo "📦 Extracting Gradle..."
  unzip -q "gradle-${GRADLE_VERSION}-all.zip" -d "$GRADLE_DIR/temp-folder"

  # Move contents to a unique hashed folder name
  HASHED=$(uuidgen | tr '[:upper:]' '[:lower:]' | cut -c1-16)
  mv "$GRADLE_DIR/temp-folder" "$GRADLE_DIR/$HASHED"

  echo "✅ Gradle ${GRADLE_VERSION} installed to:"
  echo "$GRADLE_DIR/$HASHED"
fi

# Cleanup
rm -f "gradle-${GRADLE_VERSION}-all.zip"


echo "🚀 You can now run: flutter build apk"
