import 'dart:convert';
import 'dart:math';
// import 'package:home_widget/home_widget.dart'; // Temporarily disabled
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';
import '../models/api_models.dart';
import '../../domain/entities/quote.dart';
import 'api_service.dart';
import 'offline_sync_service.dart';
import 'logging_service.dart';

// Stub implementation for HomeWidget when package is not available
class HomeWidget {
  static Future<void> setAppGroupId(String groupId) async {
    // Stub implementation - does nothing when home_widget package is not available
  }

  static Future<void> saveWidgetData<T>(String key, T value) async {
    // Stub implementation - does nothing when home_widget package is not available
  }

  static Future<void> updateWidget({String? name, String? androidName}) async {
    // Stub implementation - does nothing when home_widget package is not available
  }
}

/// Service for managing Android home screen widget
class WidgetService {
  static const String _widgetConfigKey = 'widget_config';
  static const String _lastWidgetUpdateKey = 'last_widget_update';
  static const String _widgetQuoteKey = 'widget_quote';
  
  // Widget configuration keys
  static const String widgetQuoteKey = 'widget_quote_text';
  static const String widgetAuthorKey = 'widget_quote_author';
  static const String widgetCategoryKey = 'widget_quote_category';
  static const String widgetColorKey = 'widget_category_color';
  static const String widgetTimestampKey = 'widget_timestamp';
  
  final ApiService _apiService;
  final OfflineSyncService _offlineService;
  
  WidgetService({
    required ApiService apiService,
    required OfflineSyncService offlineService,
  }) : _apiService = apiService,
       _offlineService = offlineService;

  /// Initialize widget service
  Future<void> initialize() async {
    try {
      // Initialize home widget
      await HomeWidget.setAppGroupId('group.daily_motivator');
      
      // Register background task for widget updates
      await Workmanager().initialize(callbackDispatcher, isInDebugMode: false);
      
      // Schedule periodic widget updates
      await _scheduleWidgetUpdates();
      
      LoggingService.info('Widget service initialized');
    } catch (e) {
      LoggingService.error('Error initializing widget service: $e');
    }
  }

  /// Update widget with new quote
  Future<bool> updateWidget({String? categorySlug, bool forceRefresh = false}) async {
    try {
      LoggingService.info('Updating widget...');

      Quote? quote;

      // Try to get quote from API if online
      if (await _offlineService.isOnline()) {
        final response = await _apiService.getWidgetQuote(category: categorySlug);
        if (response.isSuccess && response.data != null) {
          final widgetQuote = response.data!;
          quote = Quote(
            id: widgetQuote.id,
            text: widgetQuote.text,
            author: widgetQuote.author,
            categoryId: 1, // Default category ID
            category: widgetQuote.category,
            tags: const [],
            length: widgetQuote.text.length,
            wordCount: widgetQuote.text.split(' ').length,
            readingTime: (widgetQuote.text.split(' ').length / 200 * 60).round(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Store for offline use
          await _storeWidgetQuote(widgetQuote);
        }
      }

      // Fallback to offline quotes if API failed or offline
      quote ??= await _getOfflineWidgetQuote(categorySlug);

      if (quote != null) {
        await _updateWidgetData(quote);
        await _updateLastUpdateTimestamp();
        LoggingService.info('Widget updated successfully');
        return true;
      } else {
        LoggingService.warning('No quote available for widget update');
        return false;
      }
    } catch (e) {
      LoggingService.error('Error updating widget: $e');
      return false;
    }
  }

  /// Get widget configuration
  Future<WidgetConfig> getWidgetConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString(_widgetConfigKey);
      
      if (configJson != null) {
        final configData = json.decode(configJson);
        return WidgetConfig.fromJson(configData);
      }
      
      // Return default config
      return WidgetConfig.defaultConfig();
    } catch (e) {
      LoggingService.error('Error getting widget config: $e');
      return WidgetConfig.defaultConfig();
    }
  }

  /// Update widget configuration
  Future<void> updateWidgetConfig(WidgetConfig config) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = json.encode(config.toJson());
      await prefs.setString(_widgetConfigKey, configJson);

      // Reschedule updates with new interval
      await _scheduleWidgetUpdates();

      // Update widget immediately with new config
      await updateWidget(categorySlug: config.categoryFilter, forceRefresh: true);

      LoggingService.info('Widget config updated');
    } catch (e) {
      LoggingService.error('Error updating widget config: $e');
    }
  }

  /// Schedule periodic widget updates
  Future<void> _scheduleWidgetUpdates() async {
    try {
      final config = await getWidgetConfig();
      
      // Cancel existing tasks
      await Workmanager().cancelByUniqueName('widget_update');
      
      // Schedule new task
      await Workmanager().registerPeriodicTask(
        'widget_update',
        'widget_update_task',
        frequency: Duration(minutes: config.refreshIntervalMinutes),
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: true,
        ),
      );
      
      LoggingService.info('Widget updates scheduled every ${config.refreshIntervalMinutes} minutes');
    } catch (e) {
      LoggingService.error('Error scheduling widget updates: $e');
    }
  }

  /// Update widget data
  Future<void> _updateWidgetData(Quote quote) async {
    try {
      final config = await getWidgetConfig();
      
      await HomeWidget.saveWidgetData<String>(widgetQuoteKey, quote.text);
      await HomeWidget.saveWidgetData<String>(
        widgetAuthorKey, 
        config.showAuthor ? quote.author : ''
      );
      await HomeWidget.saveWidgetData<String>(widgetCategoryKey, quote.category);
      await HomeWidget.saveWidgetData<String>(widgetColorKey, config.backgroundColor);
      await HomeWidget.saveWidgetData<String>(
        widgetTimestampKey, 
        DateTime.now().millisecondsSinceEpoch.toString()
      );
      
      // Update widget
      await HomeWidget.updateWidget(
        name: 'DailyMotivatorWidget',
        androidName: 'DailyMotivatorWidget',
      );
    } catch (e) {
      LoggingService.error('Error updating widget data: $e');
    }
  }

  /// Get offline widget quote
  Future<Quote?> _getOfflineWidgetQuote(String? categorySlug) async {
    try {
      final quotes = await _offlineService.getStoredQuotes();

      if (quotes.isEmpty) {
        return null;
      }

      // Filter by category if specified
      List<Quote> filteredQuotes = quotes;
      if (categorySlug != null) {
        filteredQuotes = quotes.where((q) =>
          q.category.toLowerCase() == categorySlug.toLowerCase()
        ).toList();

        // Fallback to all quotes if no matches
        if (filteredQuotes.isEmpty) {
          filteredQuotes = quotes;
        }
      }

      // Get random quote
      final random = Random();
      return filteredQuotes[random.nextInt(filteredQuotes.length)];
    } catch (e) {
      LoggingService.error('Error getting offline widget quote: $e');
      return null;
    }
  }

  /// Store widget quote for offline use
  Future<void> _storeWidgetQuote(WidgetQuoteResponse quote) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quoteJson = json.encode(quote.toJson());
      await prefs.setString(_widgetQuoteKey, quoteJson);
    } catch (e) {
      LoggingService.error('Error storing widget quote: $e');
    }
  }

  /// Update last update timestamp
  Future<void> _updateLastUpdateTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastWidgetUpdateKey, DateTime.now().toIso8601String());
    } catch (e) {
      LoggingService.error('Error updating last update timestamp: $e');
    }
  }

  /// Get last update timestamp
  Future<DateTime?> getLastUpdateTimestamp() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getString(_lastWidgetUpdateKey);
      return timestamp != null ? DateTime.parse(timestamp) : null;
    } catch (e) {
      LoggingService.error('Error getting last update timestamp: $e');
      return null;
    }
  }

  /// Check if widget needs update
  Future<bool> needsUpdate() async {
    try {
      final config = await getWidgetConfig();
      final lastUpdate = await getLastUpdateTimestamp();

      if (lastUpdate == null) {
        return true;
      }

      final timeSinceUpdate = DateTime.now().difference(lastUpdate);
      return timeSinceUpdate.inMinutes >= config.refreshIntervalMinutes;
    } catch (e) {
      LoggingService.error('Error checking if widget needs update: $e');
      return true;
    }
  }

  /// Dispose resources
  void dispose() {
    // Cancel all scheduled tasks
    Workmanager().cancelAll();
  }
}

/// Widget configuration model
class WidgetConfig {
  final int refreshIntervalMinutes;
  final String? categoryFilter;
  final bool showAuthor;
  final String backgroundColor;
  final String textColor;
  final bool useApi;

  WidgetConfig({
    required this.refreshIntervalMinutes,
    this.categoryFilter,
    required this.showAuthor,
    required this.backgroundColor,
    required this.textColor,
    required this.useApi,
  });

  factory WidgetConfig.defaultConfig() {
    return WidgetConfig(
      refreshIntervalMinutes: 60,
      categoryFilter: null,
      showAuthor: true,
      backgroundColor: '#FFFFFF',
      textColor: '#000000',
      useApi: true,
    );
  }

  factory WidgetConfig.fromJson(Map<String, dynamic> json) {
    return WidgetConfig(
      refreshIntervalMinutes: json['refresh_interval_minutes'] as int? ?? 60,
      categoryFilter: json['category_filter'] as String?,
      showAuthor: json['show_author'] as bool? ?? true,
      backgroundColor: json['background_color'] as String? ?? '#FFFFFF',
      textColor: json['text_color'] as String? ?? '#000000',
      useApi: json['use_api'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'refresh_interval_minutes': refreshIntervalMinutes,
      'category_filter': categoryFilter,
      'show_author': showAuthor,
      'background_color': backgroundColor,
      'text_color': textColor,
      'use_api': useApi,
    };
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      if (task == 'widget_update_task') {
        // Initialize services
        final apiService = ApiService();
        await apiService.initialize();
        
        final offlineService = OfflineSyncService(apiService: apiService);
        final widgetService = WidgetService(
          apiService: apiService,
          offlineService: offlineService,
        );
        
        // Update widget
        await widgetService.updateWidget();
        
        return Future.value(true);
      }
      return Future.value(false);
    } catch (e) {
      LoggingService.error('Background task error: $e');
      return Future.value(false);
    }
  });
}
